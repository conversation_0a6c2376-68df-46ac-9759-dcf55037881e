## Q1: ffmpeg error/utf8 error

大体の場合、ffmpeg の問題ではなく、音声パスの問題です。<br>
ffmpeg は空白や()などの特殊文字を含むパスを読み込む際に ffmpeg error が発生する可能性があります。トレーニングセットの音声が中国語のパスを含む場合、filelist.txt に書き込む際に utf8 error が発生する可能性があります。<br>

## Q2: ワンクリックトレーニングが終わってもインデックスがない

"Training is done. The program is closed."と表示された場合、モデルトレーニングは成功しています。その直後のエラーは誤りです。<br>

ワンクリックトレーニングが終了しても added で始まるインデックスファイルがない場合、トレーニングセットが大きすぎてインデックス追加のステップが停止している可能性があります。バッチ処理 add インデックスでメモリの要求が高すぎる問題を解決しました。一時的に「トレーニングインデックス」ボタンをもう一度クリックしてみてください。<br>

## Q3: トレーニングが終了してもトレーニングセットの音色が見えない

音色をリフレッシュしてもう一度確認してください。それでも見えない場合は、トレーニングにエラーがなかったか、コンソールと WebUI のスクリーンショット、logs/実験名の下のログを開発者に送って確認してみてください。<br>

## Q4: モデルをどのように共有するか

rvc_root/logs/実験名の下に保存されている pth は、推論に使用するために共有するためのものではなく、実験の状態を保存して再現およびトレーニングを続けるためのものです。共有するためのモデルは、weights フォルダの下にある 60MB 以上の pth ファイルです。<br>
   今後、weights/exp_name.pth と logs/exp_name/added_xxx.index を組み合わせて weights/exp_name.zip にパッケージ化し、インデックスの記入ステップを省略します。その場合、zip ファイルを共有し、pth ファイルは共有しないでください。別のマシンでトレーニングを続ける場合を除きます。<br>
  logs フォルダの数百 MB の pth ファイルを weights フォルダにコピー/共有して推論に強制的に使用すると、f0、tgt_sr などのさまざまなキーが存在しないというエラーが発生する可能性があります。ckpt タブの一番下で、音高、目標オーディオサンプリングレートを手動または自動（ローカルの logs に関連情報が見つかる場合は自動的に）で選択してから、ckpt の小型モデルを抽出する必要があります（入力パスに G で始まるものを記入）。抽出が完了すると、weights フォルダに 60MB 以上の pth ファイルが表示され、音色をリフレッシュした後に使用できます。<br>

## Q5: Connection Error

コンソール（黒いウィンドウ）を閉じた可能性があります。<br>

## Q6: WebUI が Expecting value: line 1 column 1 (char 0)と表示する

システムのローカルネットワークプロキシ/グローバルプロキシを閉じてください。<br>

これはクライアントのプロキシだけでなく、サーバー側のプロキシも含まれます（例えば autodl で http_proxy と https_proxy を設定して学術的な加速を行っている場合、使用する際には unset でオフにする必要があります）。<br>

## Q7: WebUI を使わずにコマンドでトレーニングや推論を行うには

トレーニングスクリプト：<br>
まず WebUI を実行し、メッセージウィンドウにデータセット処理とトレーニング用のコマンドラインが表示されます。<br>

推論スクリプト：<br>
https://huggingface.co/lj1995/VoiceConversionWebUI/blob/main/myinfer.py<br>

例：<br>

runtime\python.exe myinfer.py 0 "E:\codes\py39\RVC-beta\todo-songs\1111.wav" "E:\codes\py39\logs\mi-test\added_IVF677_Flat_nprobe_7.index" harvest "test.wav" "weights/mi-test.pth" 0.6 cuda:0 True<br>

f0up_key=sys.argv[1]<br>
input_path=sys.argv[2]<br>
index_path=sys.argv[3]<br>
f0method=sys.argv[4]#harvest or pm<br>
opt_path=sys.argv[5]<br>
model_path=sys.argv[6]<br>
index_rate=float(sys.argv[7])<br>
device=sys.argv[8]<br>
is_half=bool(sys.argv[9])<br>

## Q8: Cuda error/Cuda out of memory

まれに cuda の設定問題やデバイスがサポートされていない可能性がありますが、大半はメモリ不足（out of memory）が原因です。<br>

トレーニングの場合は batch size を小さくします（1 にしても足りない場合はグラフィックカードを変更するしかありません）。推論の場合は、config.py の末尾にある x_pad、x_query、x_center、x_max を適宜小さくします。4GB 以下のメモリ（例えば 1060（3G）や各種 2GB のグラフィックカード）は諦めることをお勧めしますが、4GB のメモリのグラフィックカードはまだ救いがあります。<br>

## Q9: total_epoch はどのくらいに設定するのが良いですか

トレーニングセットの音質が悪く、ノイズが多い場合は、20〜30 で十分です。高すぎると、ベースモデルの音質が低音質のトレーニングセットを高めることができません。<br>
トレーニングセットの音質が高く、ノイズが少なく、長い場合は、高く設定できます。200 は問題ありません（トレーニング速度が速いので、高音質のトレーニングセットを準備できる条件がある場合、グラフィックカードも条件が良いはずなので、少しトレーニング時間が長くなることを気にすることはありません）。<br>

## Q10: トレーニングセットはどれくらいの長さが必要ですか

10 分から 50 分を推奨します。
   音質が良く、バックグラウンドノイズが低い場合、個人的な特徴のある音色であれば、多ければ多いほど良いです。
   高品質のトレーニングセット（精巧に準備された + 特徴的な音色）であれば、5 分から 10 分でも大丈夫です。リポジトリの作者もよくこの方法で遊びます。
  1 分から 2 分のデータでトレーニングに成功した人もいますが、その成功体験は他人には再現できないため、あまり参考になりません。トレーニングセットの音色が非常に特徴的である必要があります（例：高い周波数の透明な声や少女の声など）、そして音質が良い必要があります。
  1 分未満のデータでトレーニングを試みた（成功した）ケースはまだ見たことがありません。このような試みはお勧めしません。

## Q11: index rate は何に使うもので、どのように調整するのか（啓蒙）

もしベースモデルや推論ソースの音質がトレーニングセットよりも高い場合、推論結果の音質を向上させることができますが、音色がベースモデル/推論ソースの音色に近づくことがあります。これを「音色漏れ」と言います。
  index rate は音色漏れの問題を減少させたり解決するために使用されます。1 に設定すると、理論的には推論ソースの音色漏れの問題は存在しませんが、音質はトレーニングセットに近づきます。トレーニングセットの音質が推論ソースよりも低い場合、index rate を高くすると音質が低下する可能性があります。0 に設定すると、検索ミックスを利用してトレーニングセットの音色を保護する効果はありません。
   トレーニングセットが高品質で長い場合、total_epoch を高く設定することができ、この場合、モデル自体は推論ソースやベースモデルの音色をあまり参照しないため、「音色漏れ」の問題はほとんど発生しません。この時、index rate は重要ではなく、インデックスファイルを作成したり共有したりする必要もありません。

## Q11: 推論時に GPU をどのように選択するか

config.py ファイルの device cuda:の後にカード番号を選択します。
カード番号とグラフィックカードのマッピング関係は、トレーニングタブのグラフィックカード情報欄で確認できます。

## Q12: トレーニング中に保存された pth ファイルをどのように推論するか

ckpt タブの一番下で小型モデルを抽出します。

## Q13: トレーニングをどのように中断し、続行するか

現在の段階では、WebUI コンソールを閉じて go-web.bat をダブルクリックしてプログラムを再起動するしかありません。ウェブページのパラメータもリフレッシュして再度入力する必要があります。
トレーニングを続けるには：同じウェブページのパラメータでトレーニングモデルをクリックすると、前回のチェックポイントからトレーニングを続けます。

## Q14: トレーニング中にファイルページ/メモリエラーが発生した場合の対処法

プロセスが多すぎてメモリがオーバーフローしました。以下の方法で解決できるかもしれません。

1. 「音高抽出とデータ処理に使用する CPU プロセス数」を適宜下げます。
2. トレーニングセットのオーディオを手動でカットして、あまり長くならないようにします。

## Q15: 途中でデータを追加してトレーニングする方法

1. 全データに新しい実験名を作成します。
2. 前回の最新の G と D ファイル（あるいはどの中間 ckpt を基にトレーニングしたい場合は、その中間のものをコピーすることもできます）を新しい実験名にコピーします。
3. 新しい実験名でワンクリックトレーニングを開始すると、前回の最新の進捗からトレーニングを続けます。

## Q16: llvmlite.dll に関するエラー

```bash
OSError: Could not load shared object file: llvmlite.dll

FileNotFoundError: Could not find module lib\site-packages\llvmlite\binding\llvmlite.dll (or one of its dependencies). Try using the full path with constructor syntax.
```

Windows プラットフォームではこのエラーが発生しますが、https://aka.ms/vs/17/release/vc_redist.x64.exeをインストールしてWebUIを再起動すれば解決します。

## Q17: RuntimeError: テンソルの拡張サイズ（17280）は、非シングルトン次元 1 での既存サイズ（0）と一致する必要があります。 ターゲットサイズ：[1, 17280]。 テンソルサイズ：[0]

wavs16k フォルダーの下で、他のファイルよりも明らかに小さいいくつかのオーディオファイルを見つけて削除し、トレーニングモデルをクリックすればエラーは発生しませんが、ワンクリックプロセスが中断されたため、モデルのトレーニングが完了したらインデックスのトレーニングをクリックする必要があります。

## Q18: RuntimeError: テンソル a のサイズ（24）は、非シングルトン次元 2 でテンソル b（16）のサイズと一致する必要があります

トレーニング中にサンプリングレートを変更してはいけません。変更する必要がある場合は、実験名を変更して最初からトレーニングする必要があります。もちろん、前回抽出した音高と特徴（0/1/2/2b フォルダ）をコピーしてトレーニングプロセスを加速することもできます。
