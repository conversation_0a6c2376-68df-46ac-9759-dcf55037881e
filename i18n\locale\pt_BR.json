{">=3则使用对harvest音高识别的结果使用中值滤波，数值为滤波半径，使用可以削弱哑音": ">=3, use o filtro mediano para o resultado do reconhecimento do tom da he<PERSON>t, e o valor é o raio do filtro, que pode enfraquecer o mudo.", "A模型权重": "Peso (w) para o modelo A:", "A模型路径": "Caminho para o Modelo A:", "B模型路径": "Caminho para o Modelo B:", "E:\\语音音频+标注\\米津玄师\\src": "E:\\meu-dataset", "F0曲线文件, 可选, 一行一个音高, 代替默认F0及升降调": "Arquivo de curva F0 (opcional). Um arremesso por linha. Substitui a modulação padrão F0 e tom:", "Index Rate": "Taxa do Index", "Onnx导出": "Exportar Onnx", "Onnx输出路径": "Caminho de exportação ONNX:", "RVC模型路径": "Caminho do Modelo RVC:", "ckpt处理": "processamento ckpt", "harvest进程数": "Número de processos harvest", "index文件路径不可包含中文": "O caminho do arquivo de Index não pode conter caracteres chineses", "pth文件路径不可包含中文": "o caminho do arquivo pth não pode conter caracteres chineses", "rmvpe卡号配置：以-分隔输入使用的不同进程卡号,例如0-0-1使用在卡0上跑2个进程并在卡1上跑1个进程": "Configuração do número do cartão rmvpe: Use - para separar os números dos cartões de entrada de diferentes processos. Por exemplo, 0-0-1 é usado para executar 2 processos no cartão 0 e 1 processo no cartão 1.", "step1: 填写实验配置. 实验数据放在logs下, 每个实验一个文件夹, 需手工输入实验名路径, 内含实验配置, 日志, 训练得到的模型文件. ": "Etapa 1: Preencha a configuração experimental. Os dados experimentais são armazenados na pasta 'logs', com cada experimento tendo uma pasta separada. Digite manualmente o caminho do nome do experimento, que contém a configuração experimental, os logs e os arquivos de modelo treinados.", "step1:正在处理数据": "Etapa 1: Processamento de dados", "step2:正在提取音高&正在提取特征": "step2:正在提取音高&正在提取特征", "step2a: 自动遍历训练文件夹下所有可解码成音频的文件并进行切片归一化, 在实验目录下生成2个wav文件夹; 暂时只支持单人训练. ": "Etapa 2a: Percorra automaticamente todos os arquivos na pasta de treinamento que podem ser decodificados em áudio e execute a normalização da fatia. Gera 2 pastas wav no diretório do experimento. Atualmente, apenas o treinamento de um único cantor/palestrante é suportado.", "step2b: 使用CPU提取音高(如果模型带音高), 使用GPU提取特征(选择卡号)": "Etapa 2b: Use a CPU para extrair o tom (se o modelo tiver tom), use a GPU para extrair recursos (selecione o índice da GPU):", "step3: 填写训练设置, 开始训练模型和索引": "Etapa 3: <PERSON><PERSON><PERSON> as configurações de treinamento e comece a treinar o modelo e o Index", "step3a:正在训练模型": "Etapa 3a: Treinamento do modelo iniciado", "一键训练": "Treinamento com um clique", "也可批量输入音频文件, 二选一, 优先读文件夹": "Você também pode inserir arquivos de áudio em lotes. Escolha uma das duas opções. É dada prioridade à leitura da pasta.", "人声伴奏分离批量处理， 使用UVR5模型。 <br>合格的文件夹路径格式举例： E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华测试样例(去文件管理器地址栏拷就行了)。 <br>模型分为三类： <br>1、保留人声：不带和声的音频选这个，对主人声保留比HP5更好。内置HP2和HP3两个模型，HP3可能轻微漏伴奏但对主人声保留比HP2稍微好一丁点； <br>2、仅保留主人声：带和声的音频选这个，对主人声可能有削弱。内置HP5一个模型； <br> 3、去混响、去延迟模型（by FoxJoy）：<br>  (1)MDX-Net(onnx_dereverb):对于双通道混响是最好的选择，不能去除单通道混响；<br>&emsp;(234)DeEcho:去除延迟效果。Aggressive比Normal去除得更彻底，DeReverb额外去除混响，可去除单声道混响，但是对高频重的板式混响去不干净。<br>去混响/去延迟，附：<br>1、DeEcho-DeReverb模型的耗时是另外2个DeEcho模型的接近2倍；<br>2、MDX-Net-Dereverb模型挺慢的；<br>3、个人推荐的最干净的配置是先MDX-Net再DeEcho-Aggressive。": "Processamento em lote para separação de acompanhamento vocal usando o modelo UVR5.<br>Exemplo de um formato de caminho de pasta válido: D:\\caminho\\para a pasta\\entrada\\ (copie-o da barra de endereços do gerenciador de arquivos).<br>O modelo é dividido em três categorias:<br>1. Preservar vocais: Escolha esta opção para áudio sem harmonias. Ele preserva os vocais melhor do que o HP5. Inclui dois modelos integrados: HP2 e HP3. O HP3 pode vazar ligeiramente o acompanhamento, mas preserva os vocais um pouco melhor do que o HP2.<br>2 Preservar apenas os vocais principais: Escolha esta opção para áudio com harmonias. Isso pode enfraquecer os vocais principais. Ele inclui um modelo embutido: HP5.<br>3. Modelos de de-reverb e de-delay (por FoxJoy):<br>  (1) MDX-Net: A melhor escolha para remoção de reverb estéreo, mas não pode remover reverb mono;<br>&emsp;(234) DeEcho: Remove efeitos de atraso. O modo agressivo remove mais completamente do que o modo normal. O DeReverb também remove reverb e pode remover reverb mono, mas não de forma muito eficaz para conteúdo de alta frequência fortemente reverberado.<br>Notas de de-reverb/de-delay:<br>1. O tempo de processamento para o modelo DeEcho-DeReverb é aproximadamente duas vezes maior que os outros dois modelos DeEcho.<br>2 O modelo MDX-Net-Dereverb é bastante lento.<br>3. A configuração mais limpa recomendada é aplicar MDX-Net primeiro e depois DeEcho-Aggressive.", "以-分隔输入使用的卡号, 例如   0-1-2   使用卡0和卡1和卡2": "Digite o (s) índice(s) da GPU separados por '-', por exemplo, 0-1-2 para usar a GPU 0, 1 e 2:", "伴奏人声分离&去混响&去回声": "UVR5", "使用模型采样率": "使用模型采样率", "使用设备采样率": "使用设备采样率", "保存名": "Salvar nome", "保存的文件名, 默认空为和源文件同名": "Salvar nome do arquivo (padrão: igual ao arquivo de origem):", "保存的模型名不带后缀": "Nome do modelo salvo (sem extensão):", "保存频率save_every_epoch": "Faça backup a cada # de Epoch:", "保护清辅音和呼吸声，防止电音撕裂等artifact，拉满0.5不开启，调低加大保护力度但可能降低索引效果": "Proteja consoantes sem voz e sons respiratórios, evite artefatos como quebra de som eletrônico e desligue-o quando estiver cheio de 0,5. Diminua-o para aumentar a proteção, mas pode reduzir o efeito de indexação:", "修改": "<PERSON><PERSON>", "修改模型信息(仅支持weights文件夹下提取的小模型文件)": "Modificar informações do modelo (suportado apenas para arquivos de modelo pequenos extraídos da pasta 'weights')", "停止音频转换": "Conversão de áudio", "全流程结束！": "Todos os processos foram concluídos!", "刷新音色列表和索引路径": "Atualizar lista de voz e caminho do Index", "加载模型": "<PERSON><PERSON>", "加载预训练底模D路径": "Carregue o caminho D do modelo base pré-treinado:", "加载预训练底模G路径": "Carregue o caminho G do modelo base pré-treinado:", "单次推理": "Único", "卸载音色省显存": "Descarregue a voz para liberar a memória da GPU:", "变调(整数, 半音数量, 升八度12降八度-12)": "Mude o tom aqui. Se a voz for do mesmo sexo, não é necessario alterar (12 caso seja Masculino para feminino, -12 caso seja ao contrário).", "后处理重采样至最终采样率，0为不进行重采样": "Reamostragem pós-processamento para a taxa de amostragem final, 0 significa sem reamostragem:", "否": "Não", "启用相位声码器": "启用相位声码器", "响应阈值": "<PERSON><PERSON>ost<PERSON>", "响度因子": "Fator de volume", "处理数据": "Processar o Conjunto de Dados", "导出Onnx模型": "Exportar Modelo Onnx", "导出文件格式": "Qual formato de arquivo você prefere?", "常见问题解答": "FAQ (Perguntas frequentes)", "常规设置": "Configurações gerais", "开始音频转换": "Iniciar conversão de áudio", "很遗憾您这没有能用的显卡来支持您训练": "Infelizmente, não há GPU compatível disponível para apoiar o seu treinamento.", "性能设置": "Configurações de desempenho.", "总训练轮数total_epoch": "Número total de ciclos(epoch) de treino (se escolher um valor alto demais, o seu modelo parecerá terrivelmente sobretreinado):", "批量推理": "Conversão em Lote", "批量转换, 输入待转换音频文件夹, 或上传多个音频文件, 在指定文件夹(默认opt)下输出转换的音频. ": "Conversão em Massa.", "指定输出主人声文件夹": "Especifique a pasta de saída para vocais:", "指定输出文件夹": "Especifique a pasta de saída:", "指定输出非主人声文件夹": "Informar a pasta de saída para acompanhamento:", "推理时间(ms):": "Tempo de inferência (ms):", "推理音色": "Escolha o seu Modelo:", "提取": "Extrato", "提取音高和处理数据使用的CPU进程数": "Número de processos de CPU usados para extração de tom e processamento de dados:", "是": "<PERSON>m", "是否仅保存最新的ckpt文件以节省硬盘空间": "Só deve salvar apenas o arquivo ckpt mais recente para economizar espaço em disco:", "是否在每次保存时间点将最终小模型保存至weights文件夹": "Salve um pequeno modelo final na pasta 'weights' em cada ponto de salvamento:", "是否缓存所有训练集至显存. 10min以下小数据可缓存以加速训练, 大数据缓存会炸显存也加不了多少速": "Se deve armazenar em cache todos os conjuntos de treinamento na memória de vídeo. Pequenos dados com menos de 10 minutos podem ser armazenados em cache para acelerar o treinamento, e um cache de dados grande irá explodir a memória de vídeo e não aumentar muito a velocidade:", "显卡信息": "Informações da GPU", "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责. <br>如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录<b>LICENSE</b>.": "<center>The Mangio-RVC 💻 | Tradução por Krisp e <PERSON> | AI HUB BRASIL<br> Este software é de código aberto sob a licença MIT. O autor não tem qualquer controle sobre o software. Aqueles que usam o software e divulgam os sons exportados pelo software são totalmente responsáveis. <br>Se você não concorda com este termo, você não pode usar ou citar nenhum código e arquivo no pacote de software. Para obter detalhes, consulte o diretório raiz <b>O acordo a ser seguido para uso <a href='https://raw.githubusercontent.com/RVC-Project/Retrieval-based-Voice-Conversion-WebUI/main/LICENSE' target='_blank'>LICENSE</a></b></center>", "查看": "Visualizar", "查看模型信息(仅支持weights文件夹下提取的小模型文件)": "Exibir informações do modelo (suportado apenas para arquivos de modelo pequenos extraídos da pasta 'weights')", "检索特征占比": "Taxa de recurso de recuperação:", "模型": "<PERSON><PERSON>", "模型推理": "Inference", "模型提取(输入logs文件夹下大文件模型路径),适用于训一半不想训了模型没有自动提取保存小文件模型,或者想测试中间模型的情况": "Extração do modelo (insira o caminho do modelo de arquivo grande na pasta 'logs'). Isso é útil se você quiser interromper o treinamento no meio do caminho e extrair e salvar manualmente um arquivo de modelo pequeno, ou se quiser testar um modelo intermediário:", "模型是否带音高指导": "Se o modelo tem orientação de tom:", "模型是否带音高指导(唱歌一定要, 语音可以不要)": "Se o modelo tem orientação de tom (necessário para cantar, opcional para fala):", "模型是否带音高指导,1是0否": "Se o modelo tem orientação de passo (1: sim, 0: não):", "模型版本型号": "Versão:", "模型融合, 可用于测试音色融合": "A fusão modelo, pode ser usada para testar a fusão do timbre", "模型路径": "Caminho para o Modelo:", "每张显卡的batch_size": "<PERSON><PERSON> Si<PERSON> (DEIXE COMO ESTÁ a menos que saiba o que está fazendo, no Colab pode deixar até 20!):", "淡入淡出长度": "Comprimento de desvanecimento", "版本": "Vers<PERSON>", "特征提取": "Extra<PERSON>", "特征检索库文件路径,为空则使用下拉的选择结果": "Caminho para o arquivo de Index. Deixe em branco para usar o resultado selecionado no menu debaixo:", "独占 WASAPI 设备": "独占 WASAPI 设备", "男转女推荐+12key, 女转男推荐-12key, 如果音域爆炸导致音色失真也可以自己调整到合适音域. ": "Recomendado +12 chave para conversão de homem para mulher e -12 chave para conversão de mulher para homem. Se a faixa de som for muito longe e a voz estiver distorcida, você também pode ajustá-la à faixa apropriada por conta própria.", "目标采样率": "Taxa de amostragem:", "算法延迟(ms):": "Atrasos algorít<PERSON>os (ms):", "自动检测index路径,下拉式选择(dropdown)": "Detecte automaticamente o caminho do Index e selecione no menu suspenso:", "融合": "Fusão", "要改的模型信息": "Informações do modelo a ser modificado:", "要置入的模型信息": "Informações do modelo a ser colocado:", "训练": "T<PERSON>inar", "训练模型": "<PERSON><PERSON><PERSON><PERSON>", "训练特征索引": "Treinar Index", "训练结束, 您可查看控制台训练日志或实验文件夹下的train.log": "Após o término do treinamento, você pode verificar o log de treinamento do console ou train.log na pasta de experimentos", "设备类型": "设备类型", "请指定说话人id": "Especifique o ID do locutor/cantor:", "请选择index文件": "Selecione o arquivo de Index", "请选择pth文件": "Selecione o arquivo pth", "请选择说话人id": "Selecione Palestrantes/Cantores ID:", "转换": "Converter", "输入实验名": "Nome da voz:", "输入待处理音频文件夹路径": "<PERSON><PERSON><PERSON> da pasta de áudio a ser processada:", "输入待处理音频文件夹路径(去文件管理器地址栏拷就行了)": "Caminho da pasta de áudio a ser processada (copie-o da barra de endereços do gerenciador de arquivos):", "输入待处理音频文件路径(默认是正确格式示例)": "Caminho para o seu conjunto de dados (áudios, não zipado):", "输入源音量包络替换输出音量包络融合比例，越靠近1越使用输出包络": "O envelope de volume da fonte de entrada substitui a taxa de fusão do envelope de volume de saída, quanto mais próximo de 1, mais o envelope de saída é usado:", "输入监听": "Monitoramento de entrada", "输入训练文件夹路径": "<PERSON><PERSON><PERSON> da pasta de treinamento:", "输入设备": "Dispositivo de entrada", "输入降噪": "Redução de ruído de entrada", "输出信息": "Informação de saída", "输出变声": "Mudança de voz de saída", "输出设备": "Dispositivo de saída", "输出降噪": "Redução de ruído de saída", "输出音频(右下角三个点,点了可以下载)": "Exportar áudio (clique nos três pontos no canto inferior direito para baixar)", "选择.index文件": "Selecione o Index", "选择.pth文件": "Selecione o Arquivo", "选择音高提取算法,输入歌声可用pm提速,harvest低音好但巨慢无比,crepe效果好但吃GPU": "选择音高提取算法,输入歌声可用pm提速,harvest低音好但巨慢无比,crepe效果好但吃GPU", "选择音高提取算法,输入歌声可用pm提速,harvest低音好但巨慢无比,crepe效果好但吃GPU,rmvpe效果最好且微吃GPU": "Selecione o algoritmo de extração de tom \n'pm': extração mais rápida, mas discurso de qualidade inferior; \n'harvest': graves melhores, mas extremamente lentos; \n'harvest': melhor qualidade, mas extração mais lenta); 'crepe': melhor qualidade, mas intensivo em GPU; 'magio-crepe': melhor opção; 'RMVPE': um modelo robusto para estimativa de afinação vocal em música polifônica;", "选择音高提取算法:输入歌声可用pm提速,高质量语音但CPU差可用dio提速,harvest质量更好但慢,rmvpe效果最好且微吃CPU/GPU": "Selecione o algoritmo de extração de tom \n'pm': extração mais r<PERSON>, mas discurso de qualidade inferior; \n'harvest': graves melhores, mas extremamente lentos; \n'crepe': melhor qualidade (mas intensivo em GPU);\n rmvpe tem o melhor efeito e consome menos CPU/GPU.", "采样率:": "采样率:", "采样长度": "Comprimento da Amostra", "重载设备列表": "Recarregar lista de dispositivos", "音调设置": "Configurações de tom", "音频设备": "音频设备", "音高算法": "Algoritmo de detecção de pitch", "额外推理时长": "Tempo extra de inferência"}