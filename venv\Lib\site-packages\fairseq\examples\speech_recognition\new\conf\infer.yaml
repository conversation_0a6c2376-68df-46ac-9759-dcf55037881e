# @package _group_

defaults:
    - task: null
    - model: null

hydra:
  run:
    dir: ${common_eval.results_path}/${dataset.gen_subset}
  sweep:
    dir: /checkpoint/${env:USER}/${env:PREFIX}/${common_eval.results_path}
    subdir: ${dataset.gen_subset}
common_eval:
  results_path: null
  path: null
  post_process: letter
  quiet: true
dataset:
  max_tokens: 3000000
  gen_subset: test
distributed_training:
  distributed_world_size: 1
decoding:
  beam: 5
  type: viterbi
