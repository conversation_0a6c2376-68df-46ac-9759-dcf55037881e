name: Generate and Sync Locale
on:
  push:
    branches:
      - main
      - dev
jobs:
  genlocale:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@master

      - name: Run locale generation
        run: |
          python3 i18n/scan_i18n.py
          cd i18n
          python3 locale_diff.py

      - name: Commit back
        if: ${{ !github.head_ref }}
        id: commitback
        continue-on-error: true
        run: |
          git config --local user.name 'github-actions[bot]'
          git config --local user.email 'github-actions[bot]@users.noreply.github.com'
          git add --all
          git commit -m "chore(i18n): sync locale on ${{github.ref_name}}"

      - name: Create Pull Request
        if: steps.commitback.outcome == 'success'
        continue-on-error: true
        uses: peter-evans/create-pull-request@v5
        with:
          delete-branch: true
          body: "Automatically sync i18n translation jsons"
          title: "chore(i18n): sync locale on ${{github.ref_name}}"
          commit-message: "chore(i18n): sync locale on ${{github.ref_name}}"
          branch: genlocale-${{github.ref_name}}
