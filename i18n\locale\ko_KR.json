{">=3则使用对harvest音高识别的结果使用中值滤波，数值为滤波半径，使用可以削弱哑音": ">=3인 경우 harvest 피치 인식 결과에 중간값 필터 적용, 필터 반경은 값으로 지정, 사용 시 무성음 감소 가능", "A模型权重": "A 모델 가중치", "A模型路径": "A 모델 경로", "B模型路径": "B 모델 경로", "E:\\语音音频+标注\\米津玄师\\src": "E:\\음성 오디오+표시\\米津玄师\\src", "F0曲线文件, 可选, 一行一个音高, 代替默认F0及升降调": "F0 곡선 파일, 선택적, 한 줄에 하나의 피치, 기본 F0 및 음높이 조절 대체", "Index Rate": "인덱스 비율", "Onnx导出": "Onnx 내보내기", "Onnx输出路径": "Onnx 출력 경로", "RVC模型路径": "RVC 모델 경로", "ckpt处理": "ckpt 처리", "harvest进程数": "harvest 프로세스 수", "index文件路径不可包含中文": "index 파일 경로는 중국어를 포함할 수 없음", "pth文件路径不可包含中文": "pth 파일 경로는 중국어를 포함할 수 없음", "rmvpe卡号配置：以-分隔输入使用的不同进程卡号,例如0-0-1使用在卡0上跑2个进程并在卡1上跑1个进程": "rmvpe 카드 번호 설정: -로 구분된 입력 사용 카드 번호, 예: 0-0-1은 카드 0에서 2개 프로세스, 카드 1에서 1개 프로세스 실행", "step1: 填写实验配置. 实验数据放在logs下, 每个实验一个文件夹, 需手工输入实验名路径, 内含实验配置, 日志, 训练得到的模型文件. ": "step1: 실험 구성 작성. 실험 데이터는 logs에 저장, 각 실험은 하나의 폴더, 수동으로 실험 이름 경로 입력 필요, 실험 구성, 로그, 훈련된 모델 파일 포함.", "step1:正在处理数据": "step1: 데이터 처리 중", "step2:正在提取音高&正在提取特征": "step2: 음높이 추출 & 특징 추출 중", "step2a: 自动遍历训练文件夹下所有可解码成音频的文件并进行切片归一化, 在实验目录下生成2个wav文件夹; 暂时只支持单人训练. ": "step2a: 훈련 폴더 아래 모든 오디오로 디코딩 가능한 파일을 자동 순회하며 슬라이스 정규화 진행, 실험 디렉토리 아래 2개의 wav 폴더 생성; 현재 단일 사용자 훈련만 지원.", "step2b: 使用CPU提取音高(如果模型带音高), 使用GPU提取特征(选择卡号)": "step2b: CPU를 사용하여 음높이 추출(모델이 음높이 포함 시), GPU를 사용하여 특징 추출(카드 번호 선택)", "step3: 填写训练设置, 开始训练模型和索引": "step3: 훈련 설정 작성, 모델 및 인덱스 훈련 시작", "step3a:正在训练模型": "step3a: 모델 훈련 중", "一键训练": "원클릭 훈련", "也可批量输入音频文件, 二选一, 优先读文件夹": "여러 오디오 파일을 일괄 입력할 수도 있음, 둘 중 하나 선택, 폴더 우선 읽기", "人声伴奏分离批量处理， 使用UVR5模型。 <br>合格的文件夹路径格式举例： E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华测试样例(去文件管理器地址栏拷就行了)。 <br>模型分为三类： <br>1、保留人声：不带和声的音频选这个，对主人声保留比HP5更好。内置HP2和HP3两个模型，HP3可能轻微漏伴奏但对主人声保留比HP2稍微好一丁点； <br>2、仅保留主人声：带和声的音频选这个，对主人声可能有削弱。内置HP5一个模型； <br> 3、去混响、去延迟模型（by FoxJoy）：<br>  (1)MDX-Net(onnx_dereverb):对于双通道混响是最好的选择，不能去除单通道混响；<br>&emsp;(234)DeEcho:去除延迟效果。Aggressive比Normal去除得更彻底，DeReverb额外去除混响，可去除单声道混响，但是对高频重的板式混响去不干净。<br>去混响/去延迟，附：<br>1、DeEcho-DeReverb模型的耗时是另外2个DeEcho模型的接近2倍；<br>2、MDX-Net-Dereverb模型挺慢的；<br>3、个人推荐的最干净的配置是先MDX-Net再DeEcho-Aggressive。": "인간 목소리와 반주 분리 배치 처리, UVR5 모델 사용. <br>적절한 폴더 경로 예시: E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华测试样例(파일 관리자 주소 표시줄에서 복사하면 됨). <br>모델은 세 가지 유형으로 나뉨: <br>1. 인간 목소리 보존: 화음이 없는 오디오에 이것을 선택, HP5보다 주된 인간 목소리 보존에 더 좋음. 내장된 HP2와 HP3 두 모델, HP3는 약간의 반주 누락 가능성이 있지만 HP2보다 주된 인간 목소리 보존이 약간 더 좋음; <br>2. 주된 인간 목소리만 보존: 화음이 있는 오디오에 이것을 선택, 주된 인간 목소리에 약간의 약화 가능성 있음. 내장된 HP5 모델 하나; <br>3. 혼효음 제거, 지연 제거 모델(by FoxJoy):<br>  (1)MDX-Net(onnx_dereverb): 이중 채널 혼효음에는 최선의 선택, 단일 채널 혼효음은 제거할 수 없음;<br>&emsp;(234)DeEcho: 지연 제거 효과. Aggressive는 Normal보다 더 철저하게 제거, DeReverb는 추가로 혼효음을 제거, 단일 채널 혼효음은 제거 가능하지만 고주파 중심의 판 혼효음은 완전히 제거하기 어려움.<br>혼효음/지연 제거, 부록: <br>1. DeEcho-DeReverb 모델의 처리 시간은 다른 두 개의 DeEcho 모델의 거의 2배임;<br>2. MDX-Net-Dereverb 모델은 상당히 느림;<br>3. 개인적으로 추천하는 가장 깨끗한 구성은 MDX-Net 다음에 DeEcho-Aggressive 사용.", "以-分隔输入使用的卡号, 例如   0-1-2   使用卡0和卡1和卡2": "-로 구분하여 입력하는 카드 번호, 예: 0-1-2는 카드 0, 카드 1, 카드 2 사용", "伴奏人声分离&去混响&去回声": "반주 인간 목소리 분리 & 혼효음 제거 & 에코 제거", "使用模型采样率": "모델 샘플링 레이트 사용", "使用设备采样率": "장치 샘플링 레이트 사용", "保存名": "저장 이름", "保存的文件名, 默认空为和源文件同名": "저장될 파일명, 기본적으로 빈 공간은 원본 파일과 동일한 이름으로", "保存的模型名不带后缀": "저장된 모델명은 접미사 없음", "保存频率save_every_epoch": "저장 빈도 save_every_epoch", "保护清辅音和呼吸声，防止电音撕裂等artifact，拉满0.5不开启，调低加大保护力度但可能降低索引效果": "청자음과 호흡 소리를 보호, 전자음 찢김 등의 아티팩트 방지, 0.5까지 올려서 비활성화, 낮추면 보호 강도 증가하지만 인덱스 효과 감소 가능성 있음", "修改": "수정", "修改模型信息(仅支持weights文件夹下提取的小模型文件)": "모델 정보 수정(오직 weights 폴더 아래에서 추출된 작은 모델 파일만 지원)", "停止音频转换": "오디오 변환 중지", "全流程结束！": "전체 과정 완료!", "刷新音色列表和索引路径": "음색 목록 및 인덱스 경로 새로고침", "加载模型": "모델 로드", "加载预训练底模D路径": "미리 훈련된 베이스 모델 D 경로 로드", "加载预训练底模G路径": "미리 훈련된 베이스 모델 G 경로 로드", "单次推理": "단일 추론", "卸载音色省显存": "음색 언로드로 디스플레이 메모리 절약", "变调(整数, 半音数量, 升八度12降八度-12)": "키 변경(정수, 반음 수, 옥타브 상승 12, 옥타브 하강 -12)", "后处理重采样至最终采样率，0为不进行重采样": "후처리 재샘플링을 최종 샘플링 레이트로, 0은 재샘플링하지 않음", "否": "아니오", "启用相位声码器": "위상 보코더 활성화", "响应阈值": "응답 임계값", "响度因子": "음량 인자", "处理数据": "데이터 처리", "导出Onnx模型": "Onnx 모델 내보내기", "导出文件格式": "내보낼 파일 형식", "常见问题解答": "자주 묻는 질문", "常规设置": "일반 설정", "开始音频转换": "오디오 변환 시작", "很遗憾您这没有能用的显卡来支持您训练": "사용 가능한 그래픽 카드가 없어 훈련을 지원할 수 없습니다", "性能设置": "성능 설정", "总训练轮数total_epoch": "총 훈련 라운드 수 total_epoch", "批量推理": "일괄 추론", "批量转换, 输入待转换音频文件夹, 或上传多个音频文件, 在指定文件夹(默认opt)下输出转换的音频. ": "일괄 변환, 변환할 오디오 파일 폴더 입력 또는 여러 오디오 파일 업로드, 지정된 폴더(기본값 opt)에 변환된 오디오 출력.", "指定输出主人声文件夹": "주된 목소리 출력 폴더 지정", "指定输出文件夹": "출력 파일 폴더 지정", "指定输出非主人声文件夹": "주된 목소리가 아닌 출력 폴더 지정", "推理时间(ms):": "추론 시간(ms):", "推理音色": "추론 음색", "提取": "추출", "提取音高和处理数据使用的CPU进程数": "음높이 추출 및 데이터 처리에 사용되는 CPU 프로세스 수", "是": "예", "是否仅保存最新的ckpt文件以节省硬盘空间": "디스크 공간을 절약하기 위해 최신 ckpt 파일만 저장할지 여부", "是否在每次保存时间点将最终小模型保存至weights文件夹": "저장 시마다 최종 소형 모델을 weights 폴더에 저장할지 여부", "是否缓存所有训练集至显存. 10min以下小数据可缓存以加速训练, 大数据缓存会炸显存也加不了多少速": "모든 훈련 세트를 VRAM에 캐시할지 여부. 10분 미만의 소량 데이터는 캐시하여 훈련 속도를 높일 수 있지만, 대량 데이터 캐시는 VRAM을 과부하시키고 속도를 크게 향상시키지 못함", "显卡信息": "그래픽 카드 정보", "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责. <br>如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录<b>LICENSE</b>.": "이 소프트웨어는 MIT 라이선스로 공개되며, 저자는 소프트웨어에 대해 어떠한 통제권도 가지지 않습니다. 모든 귀책사유는 소프트웨어 사용자 및 소프트웨어에서 생성된 결과물을 사용하는 당사자에게 있습니다. <br>해당 조항을 인정하지 않는 경우, 소프트웨어 패키지의 어떠한 코드나 파일도 사용하거나 인용할 수 없습니다. 자세한 내용은 루트 디렉토리의 <b>LICENSE</b>를 참조하세요.", "查看": "보기", "查看模型信息(仅支持weights文件夹下提取的小模型文件)": "모델 정보 보기(오직 weights 폴더에서 추출된 소형 모델 파일만 지원)", "检索特征占比": "검색 특징 비율", "模型": "모델", "模型推理": "모델 추론", "模型提取(输入logs文件夹下大文件模型路径),适用于训一半不想训了模型没有自动提取保存小文件模型,或者想测试中间模型的情况": "모델 추출(logs 폴더 아래의 큰 파일 모델 경로 입력), 훈련 중간에 중단한 모델의 자동 추출 및 소형 파일 모델 저장이 안 되거나 중간 모델을 테스트하고 싶은 경우에 적합", "模型是否带音高指导": "모델이 음높이 지도를 포함하는지 여부", "模型是否带音高指导(唱歌一定要, 语音可以不要)": "모델이 음높이 지도를 포함하는지 여부(노래에는 반드시 필요, 음성에는 필요 없음)", "模型是否带音高指导,1是0否": "모델이 음높이 지도를 포함하는지 여부, 1은 예, 0은 아니오", "模型版本型号": "모델 버전 및 모델", "模型融合, 可用于测试音色融合": "모델 융합, 음색 융합 테스트에 사용 가능", "模型路径": "모델 경로", "每张显卡的batch_size": "각 그래픽 카드의 batch_size", "淡入淡出长度": "페이드 인/아웃 길이", "版本": "버전", "特征提取": "특징 추출", "特征检索库文件路径,为空则使用下拉的选择结果": "특징 검색 라이브러리 파일 경로, 비어 있으면 드롭다운 선택 결과 사용", "独占 WASAPI 设备": "独占 WASAPI 设备", "男转女推荐+12key, 女转男推荐-12key, 如果音域爆炸导致音色失真也可以自己调整到合适音域. ": "남성에서 여성으로 변경 시 +12 키 권장, 여성에서 남성으로 변경 시 -12 키 권장, 음역대 폭발로 음색이 왜곡되면 적절한 음역대로 조정 가능.", "目标采样率": "목표 샘플링률", "算法延迟(ms):": "알고리즘 지연(ms):", "自动检测index路径,下拉式选择(dropdown)": "자동으로 index 경로 감지, 드롭다운 선택(dropdown)", "融合": "융합", "要改的模型信息": "변경할 모델 정보", "要置入的模型信息": "삽입할 모델 정보", "训练": "훈련", "训练模型": "모델 훈련", "训练特征索引": "특징 인덱스 훈련", "训练结束, 您可查看控制台训练日志或实验文件夹下的train.log": "훈련 완료, 콘솔 훈련 로그 또는 실험 폴더 내의 train.log 확인 가능", "设备类型": "设备类型", "请指定说话人id": "화자 ID 지정 필요", "请选择index文件": "index 파일 선택", "请选择pth文件": "pth 파일 선택", "请选择说话人id": "화자 ID 선택", "转换": "변환", "输入实验名": "실험명 입력", "输入待处理音频文件夹路径": "처리할 오디오 파일 폴더 경로 입력", "输入待处理音频文件夹路径(去文件管理器地址栏拷就行了)": "처리할 오디오 파일 폴더 경로 입력(파일 탐색기 주소 표시줄에서 복사)", "输入待处理音频文件路径(默认是正确格式示例)": "처리할 오디오 파일 경로 입력(기본적으로 올바른 형식 예시)", "输入源音量包络替换输出音量包络融合比例，越靠近1越使用输出包络": "입력 소스 볼륨 엔벨로프와 출력 볼륨 엔벨로프의 결합 비율 입력, 1에 가까울수록 출력 엔벨로프 사용", "输入监听": "입력 모니터링", "输入训练文件夹路径": "훈련 파일 폴더 경로 입력", "输入设备": "입력 장치", "输入降噪": "입력 노이즈 감소", "输出信息": "출력 정보", "输出变声": "출력 음성 변조", "输出设备": "출력 장치", "输出降噪": "출력 노이즈 감소", "输出音频(右下角三个点,点了可以下载)": "출력 오디오(오른쪽 하단 세 개의 점, 클릭하면 다운로드 가능)", "选择.index文件": ".index 파일 선택", "选择.pth文件": ".pth 파일 선택", "选择音高提取算法,输入歌声可用pm提速,harvest低音好但巨慢无比,crepe效果好但吃GPU": "음높이 추출 알고리즘 선택, 노래 입력 시 pm으로 속도 향상, harvest는 저음이 좋지만 매우 느림, crepe는 효과가 좋지만 GPU 사용", "选择音高提取算法,输入歌声可用pm提速,harvest低音好但巨慢无比,crepe效果好但吃GPU,rmvpe效果最好且微吃GPU": "음높이 추출 알고리즘 선택, 노래 입력 시 pm으로 속도 향상, harvest는 저음이 좋지만 매우 느림, crepe는 효과가 좋지만 GPU 사용, rmvpe는 효과가 가장 좋으며 GPU를 적게 사용", "选择音高提取算法:输入歌声可用pm提速,高质量语音但CPU差可用dio提速,harvest质量更好但慢,rmvpe效果最好且微吃CPU/GPU": "음높이 추출 알고리즘 선택: 노래 입력 시 pm으로 속도 향상, 고품질 음성에는 CPU가 부족할 때 dio 사용, harvest는 품질이 더 좋지만 느림, rmvpe는 효과가 가장 좋으며 CPU/GPU를 적게 사용", "采样率:": "샘플링률:", "采样长度": "샘플링 길이", "重载设备列表": "장치 목록 재로드", "音调设置": "음조 설정", "音频设备": "音频设备", "音高算法": "음높이 알고리즘", "额外推理时长": "추가 추론 시간"}