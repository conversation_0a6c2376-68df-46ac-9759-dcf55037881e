## Q1:ffmpeg 오류/utf8 오류

대부분의 경우 ffmpeg 문제가 아니라 오디오 경로 문제입니다. <br>
ffmpeg가 공백, () 등의 특수 문자가 포함된 경로를 읽을 때 ffmpeg 오류가 발생할 수 있습니다. 트레이닝 세트 오디오가 중문 경로일 때 filelist.txt에 쓸 때 utf8 오류가 발생할 수 있습니다. <br>

## Q2:일괄 트레이닝이 끝나고 인덱스가 없음

"Training is done. The program is closed."라고 표시되면 모델 트레이닝이 성공한 것이며, 이어지는 오류는 가짜입니다. <br>

일괄 트레이닝이 끝나고 'added'로 시작하는 인덱스 파일이 없으면 트레이닝 세트가 너무 커서 인덱스 추가 단계에서 멈췄을 수 있습니다. 메모리에 대한 인덱스 추가 요구 사항이 너무 큰 문제를 배치 처리 add 인덱스로 해결했습니다. 임시로 "트레이닝 인덱스" 버튼을 다시 클릭해 보세요. <br>

## Q3:트레이닝이 끝나고 트레이닝 세트의 음색을 추론에서 보지 못함

'음색 새로고침'을 클릭해 보세요. 여전히 없다면 트레이닝에 오류가 있는지, 콘솔 및 webui의 스크린샷, logs/실험명 아래의 로그를 개발자에게 보내 확인해 보세요. <br>

## Q4:모델 공유 방법

rvc_root/logs/실험명 아래에 저장된 pth는 추론에 사용하기 위한 것이 아니라 실험 상태를 저장하고 복원하며, 트레이닝을 계속하기 위한 것입니다. 공유에 사용되는 모델은 weights 폴더 아래 60MB 이상인 pth 파일입니다. <br>
<br/>
향후에는 weights/exp_name.pth와 logs/exp_name/added_xxx.index를 결합하여 weights/exp_name.zip으로 만들어 index 입력 단계를 생략할 예정입니다. 그러면 zip 파일을 공유하고 pth 파일은 공유하지 마세요. 단지 다른 기계에서 트레이닝을 계속하려는 경우에만 공유하세요. <br>
<br/>
logs 폴더 아래 수백 MB의 pth 파일을 weights 폴더에 복사/공유하여 강제로 추론에 사용하면 f0, tgt_sr 등의 키가 없다는 오류가 발생할 수 있습니다. ckpt 탭 아래에서 수동 또는 자동(로컬 logs에서 관련 정보를 찾을 수 있는 경우 자동)으로 음성, 대상 오디오 샘플링률 옵션을 선택한 후 ckpt 소형 모델을 추출해야 합니다(입력 경로에 G로 시작하는 경로를 입력). 추출 후 weights 폴더에 60MB 이상의 pth 파일이 생성되며, 음색 새로고침 후 사용할 수 있습니다. <br>

## Q5:연결 오류

아마도 컨트롤 콘솔(검은 창)을 닫았을 것입니다. <br>

## Q6:WebUI에서 "Expecting value: line 1 column 1 (char 0)" 오류가 발생함

시스템 로컬 네트워크 프록시/글로벌 프록시를 닫으세요. <br>

이는 클라이언트의 프록시뿐만 아니라 서버 측의 프록시도 포함합니다(예: autodl로 http_proxy 및 https_proxy를 설정한 경우 사용 시 unset으로 끄세요). <br>

## Q7:WebUI 없이 명령으로 트레이닝 및 추론하는 방법

트레이닝 스크립트: <br>
먼저 WebUI를 실행하여 데이터 세트 처리 및 트레이닝에 사용되는 명령줄을 메시지 창에서 확인할 수 있습니다. <br>

추론 스크립트: <br>
https://huggingface.co/lj1995/VoiceConversionWebUI/blob/main/myinfer.py <br>

예제: <br>

runtime\python.exe myinfer.py 0 "E:\codes\py39\RVC-beta\todo-songs\1111.wav" "E:\codes\py39\logs\mi-test\added_IVF677_Flat_nprobe_7.index" harvest "test.wav" "weights/mi-test.pth" 0.6 cuda:0 True <br>

f0up_key=sys.argv[1] <br>
input_path=sys.argv[2] <br>
index_path=sys.argv[3] <br>
f0method=sys.argv[4]#harvest 또는 pm <br>
opt_path=sys.argv[5] <br>
model_path=sys.argv[6] <br>
index_rate=float(sys.argv[7]) <br>
device=sys.argv[8] <br>
is_half=bool(sys.argv[9]) <br>

## Q8:Cuda 오류/Cuda 메모리 부족

아마도 cuda 설정 문제이거나 장치가 지원되지 않을 수 있습니다. 대부분의 경우 메모리가 부족합니다(out of memory). <br>

트레이닝의 경우 batch size를 줄이세요(1로 줄여도 부족하다면 다른 그래픽 카드로 트레이닝을 해야 합니다). 추론의 경우 config.py 파일 끝에 있는 x_pad, x_query, x_center, x_max를 적절히 줄이세요. 4GB 미만의 메모리(예: 1060(3GB) 및 여러 2GB 그래픽 카드)를 가진 경우는 포기하세요. 4GB 메모리 그래픽 카드는 아직 구할 수 있습니다. <br>

## Q9:total_epoch를 몇으로 설정하는 것이 좋을까요

트레이닝 세트의 오디오 품질이 낮고 배경 소음이 많으면 20~30이면 충분합니다. 너무 높게 설정하면 바닥 모델의 오디오 품질이 낮은 트레이닝 세트를 높일 수 없습니다. <br>
트레이닝 세트의 오디오 품질이 높고 배경 소음이 적고 길이가 길 경우 높게 설정할 수 있습니다. 200도 괜찮습니다(트레이닝 속도가 빠르므로, 고품질 트레이닝 세트를 준비할 수 있는 조건이 있다면, 그래픽 카드도 좋을 것이므로, 조금 더 긴 트레이닝 시간에 대해 걱정하지 않을 것입니다). <br>

## Q10: 트레이닝 세트는 얼마나 길어야 하나요

10분에서 50분을 추천합니다.
<br/>
음질이 좋고 백그라운드 노이즈가 낮은 상태에서, 개인적인 특색 있는 음색이라면 더 많으면 더 좋습니다.
<br/>
고품질의 트레이닝 세트(정교하게 준비된 + 특색 있는 음색)라면, 5분에서 10분도 괜찮습니다. 저장소의 저자도 종종 이렇게 합니다.
<br/>
1분에서 2분의 데이터로 트레이닝에 성공한 사람도 있지만, 그러한 성공 사례는 다른 사람이 재현하기 어려우며 참고 가치가 크지 않습니다. 이는 트레이닝 세트의 음색이 매우 뚜렷해야 하며(예: 높은 주파수의 명확한 목소리나 소녀음) 음질이 좋아야 합니다.
<br/>
1분 미만의 데이터로 트레이닝을 시도(성공)한 사례는 아직 보지 못했습니다. 이런 시도는 권장하지 않습니다.

## Q11: index rate는 무엇이며, 어떻게 조정하나요? (과학적 설명)

만약 베이스 모델과 추론 소스의 음질이 트레이닝 세트보다 높다면, 그들은 추론 결과의 음질을 높일 수 있지만, 음색이 베이스 모델/추론 소스의 음색으로 기울어질 수 있습니다. 이 현상을 "음색 유출"이라고 합니다.
<br/>
index rate는 음색 유출 문제를 줄이거나 해결하는 데 사용됩니다. 1로 조정하면 이론적으로 추론 소스의 음색 유출 문제가 없지만, 음질은 트레이닝 세트에 더 가깝게 됩니다. 만약 트레이닝 세트의 음질이 추론 소스보다 낮다면, index rate를 높이면 음질이 낮아질 수 있습니다. 0으로 조정하면 검색 혼합을 이용하여 트레이닝 세트의 음색을 보호하는 효과가 없습니다.
<br/>
트레이닝 세트가 고품질이고 길이가 길 경우, total_epoch를 높일 수 있으며, 이 경우 모델 자체가 추론 소스와 베이스 모델의 음색을 거의 참조하지 않아 "음색 유출" 문제가 거의 발생하지 않습니다. 이때 index rate는 중요하지 않으며, 심지어 index 색인 파일을 생성하거나 공유하지 않아도 됩니다.

## Q11: 추론시 GPU를 어떻게 선택하나요?

config.py 파일에서 device cuda: 다음에 카드 번호를 선택합니다.
카드 번호와 그래픽 카드의 매핑 관계는 트레이닝 탭의 그래픽 카드 정보란에서 볼 수 있습니다.

## Q12: 트레이닝 중간에 저장된 pth를 어떻게 추론하나요?

ckpt 탭 하단에서 소형 모델을 추출합니다.

## Q13: 트레이닝을 어떻게 중단하고 계속할 수 있나요?

현재 단계에서는 WebUI 콘솔을 닫고 go-web.bat을 더블 클릭하여 프로그램을 다시 시작해야 합니다. 웹 페이지 매개변수도 새로 고쳐서 다시 입력해야 합니다.
트레이닝을 계속하려면: 같은 웹 페이지 매개변수로 트레이닝 모델을 클릭하면 이전 체크포인트에서 트레이닝을 계속합니다.

## Q14: 트레이닝 중 파일 페이지/메모리 오류가 발생하면 어떻게 해야 하나요?

프로세스가 너무 많이 열려 메모리가 폭발했습니다. 다음과 같은 방법으로 해결할 수 있습니다.

1. "음높이 추출 및 데이터 처리에 사용되는 CPU 프로세스 수"를 적당히 낮춥니다.
2. 트레이닝 세트 오디오를 수동으로 잘라 너무 길지 않게 합니다.

## Q15: 트레이닝 도중 데이터를 어떻게 추가하나요?

1. 모든 데이터에 새로운 실험 이름을 만듭니다.
2. 이전에 가장 최신의 G와 D 파일(또는 어떤 중간 ckpt를 기반으로 트레이닝하고 싶다면 중간 것을 복사할 수도 있음)을 새 실험 이름으로 복사합니다.
3. 새 실험 이름으로 원클릭 트레이닝을 시작하면 이전의 최신 진행 상황에서 계속 트레이닝합니다.

## Q16: llvmlite.dll에 관한 오류

```bash
OSError: Could not load shared object file: llvmlite.dll

FileNotFoundError: Could not find module lib\site-packages\llvmlite\binding\llvmlite.dll (or one of its dependencies). Try using the full path with constructor syntax.
```

Windows 플랫폼에서 이 오류가 발생하면 https://aka.ms/vs/17/release/vc_redist.x64.exe를 설치하고 WebUI를 다시 시작하면 해결됩니다.

## Q17: RuntimeError: 텐서의 확장된 크기(17280)는 비 단일 항목 차원 1에서 기존 크기(0)와 일치해야 합니다. 대상 크기: [1, 17280]. 텐서 크기: [0]

wavs16k 폴더 아래에서 다른 파일들보다 크기가 현저히 작은 일부 오디오 파일을 찾아 삭제하고, 트레이닝 모델을 클릭하면 오류가 발생하지 않습니다. 하지만 원클릭 프로세스가 중단되었기 때문에 모델 트레이닝이 완료된 후에는 인덱스 트레이닝을 클릭해야 합니다.

## Q18: RuntimeError: 텐서 a의 크기(24)가 비 단일 항목 차원 2에서 텐서 b(16)의 크기와 일치해야 합니다.

트레이닝 도중에 샘플링 레이트를 변경해서는 안 됩니다. 변경해야 한다면 실험 이름을 변경하고 처음부터 트레이닝해야 합니다. 물론, 이전에 추출한 음높이와 특징(0/1/2/2b 폴더)을 복사하여 트레이닝 프로세스를 가속화할 수도 있습니다.
