本软件及其相关代码以MIT协议开源，作者不对软件具备任何控制力，使用软件者、传播软件导出的声音者自负全责。
如不认可该条款，则不能使用或引用软件包内任何代码和文件。

特此授予任何获得本软件和相关文档文件（以下简称“软件”）副本的人免费使用、复制、修改、合并、出版、分发、再授权和/或销售本软件的权利，以及授予本软件所提供的人使用本软件的权利，但须符合以下条件：
上述版权声明和本许可声明应包含在软件的所有副本或实质部分中。
软件是“按原样”提供的，没有任何明示或暗示的保证，包括但不限于适销性、适用于特定目的和不侵权的保证。在任何情况下，作者或版权持有人均不承担因软件或软件的使用或其他交易而产生、产生或与之相关的任何索赔、损害赔偿或其他责任，无论是在合同诉讼、侵权诉讼还是其他诉讼中。


The LICENCEs for related libraries are as follows.
相关引用库协议如下：

ContentVec
https://github.com/auspicious3000/contentvec/blob/main/LICENSE
MIT License

VITS
https://github.com/jaywalnut310/vits/blob/main/LICENSE
MIT License

HIFIGAN
https://github.com/jik876/hifi-gan/blob/master/LICENSE
MIT License

gradio
https://github.com/gradio-app/gradio/blob/main/LICENSE
Apache License 2.0

ffmpeg
https://github.com/FFmpeg/FFmpeg/blob/master/COPYING.LGPLv3
https://github.com/BtbN/FFmpeg-Builds/releases/download/autobuild-2021-02-28-12-32/ffmpeg-n4.3.2-160-gfbb9368226-win64-lgpl-4.3.zip
LPGLv3 License
MIT License

ultimatevocalremovergui
https://github.com/Anjok07/ultimatevocalremovergui/blob/master/LICENSE
https://github.com/yang123qwe/vocal_separation_by_uvr5
MIT License

audio-slicer
https://github.com/openvpi/audio-slicer/blob/main/LICENSE
MIT License

PySimpleGUI
https://github.com/PySimpleGUI/PySimpleGUI/blob/master/license.txt
LPGLv3 License
