# @package _group_

defaults:
  - model: null

hydra:
  run:
    dir: ${common_eval.results_path}/viterbi
  sweep:
    dir: ${common_eval.results_path}
    subdir: viterbi

task:
  _name: hubert_pretraining
  single_target: true
  fine_tuning: true
  data: ???
  normalize: ???

decoding:
  type: viterbi
  unique_wer_file: true
common_eval:
  results_path: ???
  path: ???
  post_process: letter
dataset:
  max_tokens: 1100000
  gen_subset: ???
