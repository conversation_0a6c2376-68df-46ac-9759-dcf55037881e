name: pydml
channels:
  - pytorch
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/free/
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/conda-forge/
  - defaults
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/fastai/
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/pytorch/
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/bioconda/
dependencies:
  - abseil-cpp=20211102.0=hd77b12b_0
  - absl-py=1.3.0=py310haa95532_0
  - aiohttp=3.8.3=py310h2bbff1b_0
  - aiosignal=1.2.0=pyhd3eb1b0_0
  - async-timeout=4.0.2=py310haa95532_0
  - attrs=22.1.0=py310haa95532_0
  - blas=1.0=mkl
  - blinker=1.4=py310haa95532_0
  - bottleneck=1.3.5=py310h9128911_0
  - brotli=1.0.9=h2bbff1b_7
  - brotli-bin=1.0.9=h2bbff1b_7
  - brotlipy=0.7.0=py310h2bbff1b_1002
  - bzip2=1.0.8=he774522_0
  - c-ares=1.19.0=h2bbff1b_0
  - ca-certificates=2023.05.30=haa95532_0
  - cachetools=4.2.2=pyhd3eb1b0_0
  - certifi=2023.5.7=py310haa95532_0
  - cffi=1.15.1=py310h2bbff1b_3
  - charset-normalizer=2.0.4=pyhd3eb1b0_0
  - click=8.0.4=py310haa95532_0
  - colorama=0.4.6=py310haa95532_0
  - contourpy=1.0.5=py310h59b6b97_0
  - cryptography=39.0.1=py310h21b164f_0
  - cycler=0.11.0=pyhd3eb1b0_0
  - fonttools=4.25.0=pyhd3eb1b0_0
  - freetype=2.12.1=ha860e81_0
  - frozenlist=1.3.3=py310h2bbff1b_0
  - giflib=5.2.1=h8cc25b3_3
  - glib=2.69.1=h5dc1a3c_2
  - google-auth=2.6.0=pyhd3eb1b0_0
  - google-auth-oauthlib=0.4.4=pyhd3eb1b0_0
  - grpc-cpp=1.48.2=hf108199_0
  - grpcio=1.48.2=py310hf108199_0
  - gst-plugins-base=1.18.5=h9e645db_0
  - gstreamer=1.18.5=hd78058f_0
  - icu=58.2=ha925a31_3
  - idna=3.4=py310haa95532_0
  - intel-openmp=2023.1.0=h59b6b97_46319
  - jpeg=9e=h2bbff1b_1
  - kiwisolver=1.4.4=py310hd77b12b_0
  - krb5=1.19.4=h5b6d351_0
  - lerc=3.0=hd77b12b_0
  - libbrotlicommon=1.0.9=h2bbff1b_7
  - libbrotlidec=1.0.9=h2bbff1b_7
  - libbrotlienc=1.0.9=h2bbff1b_7
  - libclang=14.0.6=default_hb5a9fac_1
  - libclang13=14.0.6=default_h8e68704_1
  - libdeflate=1.17=h2bbff1b_0
  - libffi=3.4.4=hd77b12b_0
  - libiconv=1.16=h2bbff1b_2
  - libogg=1.3.5=h2bbff1b_1
  - libpng=1.6.39=h8cc25b3_0
  - libprotobuf=3.20.3=h23ce68f_0
  - libtiff=4.5.0=h6c2663c_2
  - libuv=1.44.2=h2bbff1b_0
  - libvorbis=1.3.7=he774522_0
  - libwebp=1.2.4=hbc33d0d_1
  - libwebp-base=1.2.4=h2bbff1b_1
  - libxml2=2.10.3=h0ad7f3c_0
  - libxslt=1.1.37=h2bbff1b_0
  - lz4-c=1.9.4=h2bbff1b_0
  - markdown=3.4.1=py310haa95532_0
  - markupsafe=2.1.1=py310h2bbff1b_0
  - matplotlib=3.7.1=py310haa95532_1
  - matplotlib-base=3.7.1=py310h4ed8f06_1
  - mkl=2023.1.0=h8bd8f75_46356
  - mkl-service=2.4.0=py310h2bbff1b_1
  - mkl_fft=1.3.6=py310h4ed8f06_1
  - mkl_random=1.2.2=py310h4ed8f06_1
  - multidict=6.0.2=py310h2bbff1b_0
  - munkres=1.1.4=py_0
  - numexpr=2.8.4=py310h2cd9be0_1
  - numpy=1.24.3=py310h055cbcc_1
  - numpy-base=1.24.3=py310h65a83cf_1
  - oauthlib=3.2.2=py310haa95532_0
  - openssl=1.1.1t=h2bbff1b_0
  - packaging=23.0=py310haa95532_0
  - pandas=1.5.3=py310h4ed8f06_0
  - pcre=8.45=hd77b12b_0
  - pillow=9.4.0=py310hd77b12b_0
  - pip=23.0.1=py310haa95532_0
  - ply=3.11=py310haa95532_0
  - protobuf=3.20.3=py310hd77b12b_0
  - pyasn1=0.4.8=pyhd3eb1b0_0
  - pyasn1-modules=0.2.8=py_0
  - pycparser=2.21=pyhd3eb1b0_0
  - pyjwt=2.4.0=py310haa95532_0
  - pyopenssl=23.0.0=py310haa95532_0
  - pyparsing=3.0.9=py310haa95532_0
  - pyqt=5.15.7=py310hd77b12b_0
  - pyqt5-sip=12.11.0=py310hd77b12b_0
  - pysocks=1.7.1=py310haa95532_0
  - python=3.10.11=h966fe2a_2
  - python-dateutil=2.8.2=pyhd3eb1b0_0
  - pytorch-mutex=1.0=cpu
  - pytz=2022.7=py310haa95532_0
  - pyyaml=6.0=py310h2bbff1b_1
  - qt-main=5.15.2=he8e5bd7_8
  - qt-webengine=5.15.9=hb9a9bb5_5
  - qtwebkit=5.212=h2bbfb41_5
  - re2=2022.04.01=hd77b12b_0
  - requests=2.29.0=py310haa95532_0
  - requests-oauthlib=1.3.0=py_0
  - rsa=4.7.2=pyhd3eb1b0_1
  - setuptools=67.8.0=py310haa95532_0
  - sip=6.6.2=py310hd77b12b_0
  - six=1.16.0=pyhd3eb1b0_1
  - sqlite=3.41.2=h2bbff1b_0
  - tbb=2021.8.0=h59b6b97_0
  - tensorboard=2.10.0=py310haa95532_0
  - tensorboard-data-server=0.6.1=py310haa95532_0
  - tensorboard-plugin-wit=1.8.1=py310haa95532_0
  - tk=8.6.12=h2bbff1b_0
  - toml=0.10.2=pyhd3eb1b0_0
  - tornado=6.2=py310h2bbff1b_0
  - tqdm=4.65.0=py310h9909e9c_0
  - typing_extensions=4.5.0=py310haa95532_0
  - tzdata=2023c=h04d1e81_0
  - urllib3=1.26.16=py310haa95532_0
  - vc=14.2=h21ff451_1
  - vs2015_runtime=14.27.29016=h5e58377_2
  - werkzeug=2.2.3=py310haa95532_0
  - wheel=0.38.4=py310haa95532_0
  - win_inet_pton=1.1.0=py310haa95532_0
  - xz=5.4.2=h8cc25b3_0
  - yaml=0.2.5=he774522_0
  - yarl=1.8.1=py310h2bbff1b_0
  - zlib=1.2.13=h8cc25b3_0
  - zstd=1.5.5=hd43e919_0
  - pip:
      - antlr4-python3-runtime==4.8
      - appdirs==1.4.4
      - audioread==3.0.0
      - bitarray==2.7.4
      - cython==0.29.35
      - decorator==5.1.1
      - fairseq==0.12.2
      - faiss-cpu==1.7.4
      - filelock==3.12.0
      - hydra-core==1.0.7
      - jinja2==3.1.2
      - joblib==1.2.0
      - lazy-loader==0.2
      - librosa==0.10.0.post2
      - llvmlite==0.40.0
      - lxml==4.9.2
      - mpmath==1.3.0
      - msgpack==1.0.5
      - networkx==3.1
      - noisereduce==2.0.1
      - numba==0.57.0
      - omegaconf==2.0.6
      - opencv-python==********
      - pooch==1.6.0
      - portalocker==2.7.0
      - pysimplegui==4.60.5
      - pywin32==306
      - pyworld==0.3.3
      - regex==2023.5.5
      - sacrebleu==2.3.1
      - scikit-learn==1.2.2
      - scipy==1.10.1
      - sounddevice==0.4.6
      - soundfile==0.12.1
      - soxr==0.3.5
      - sympy==1.12
      - tabulate==0.9.0
      - threadpoolctl==3.1.0
      - torch==2.0.0
      - torch-directml==0.2.0.dev230426
      - torchaudio==2.0.1
      - torchvision==0.15.1
      - wget==3.2
prefix: D:\ProgramData\anaconda3_\envs\pydml
