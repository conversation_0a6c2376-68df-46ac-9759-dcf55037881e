../../Scripts/fairseq-eval-lm.exe,sha256=e4Me287hho2On6GbwTMEccymtqIwz71bFqIHdzGT4-M,108445
../../Scripts/fairseq-generate.exe,sha256=6-q997ny87y2CstnAv_2WbdUp_sdHwglAhysdz8VSsQ,108446
../../Scripts/fairseq-hydra-train.exe,sha256=GbO2hDBgvmQHOS2DcaKpaQMRmite6FboObhbgM-MIYM,108449
../../Scripts/fairseq-interactive.exe,sha256=Otx3EuVYBPCR3c-1lS6WReQ3pFpVktnZ3L1j9kv2mp4,108449
../../Scripts/fairseq-preprocess.exe,sha256=agvZMKwG_5BWYIHB6JQHIo_wWEnrZLgwto1GE9gTeV0,108448
../../Scripts/fairseq-score.exe,sha256=Wuth28WI-3oJ_5NLlMlEPn4G6s21G6tqsQDE6a1UpIY,108443
../../Scripts/fairseq-train.exe,sha256=wrlBOcSMfiYkNBXnac_7TdR-2SfPvcLUdqCeJtHL0vg,108443
../../Scripts/fairseq-validate.exe,sha256=mSludfPZoMHZCeoym6yJFCj5MPKwwXg9-NeG7PJMlp8,108446
fairseq-0.12.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
fairseq-0.12.2.dist-info/METADATA,sha256=x4lyUCY9wcLWgsMbR9MxnnOTT8aL_OtUko_61KFFO_E,18653
fairseq-0.12.2.dist-info/RECORD,,
fairseq-0.12.2.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
fairseq-0.12.2.dist-info/WHEEL,sha256=JLOMsP7F5qtkAkINx5UnzbFguf8CqZeraV8o04b0I8I,101
fairseq-0.12.2.dist-info/entry_points.txt,sha256=cCHOGEyWo0dTqKu7ptA0IzSXRAVoIW7g6usnu8j70qM,412
fairseq-0.12.2.dist-info/licenses/LICENSE,sha256=UkEte8fOQVfqYou6rLiCngqcs8WPV_mRdhJryM8r_IU,1086
fairseq-0.12.2.dist-info/top_level.txt,sha256=aIkcWuogkvfIoSZnXjQMdJ0wKjIYxEm1e8cToLPvUmk,20
fairseq/__init__.py,sha256=9ULg8lZeZwGjiBo9JLone1wHulskUWkchK1ejE2gHgQ,1337
fairseq/__pycache__/__init__.cpython-311.pyc,,
fairseq/__pycache__/binarizer.cpython-311.pyc,,
fairseq/__pycache__/checkpoint_utils.cpython-311.pyc,,
fairseq/__pycache__/file_chunker_utils.cpython-311.pyc,,
fairseq/__pycache__/file_io.cpython-311.pyc,,
fairseq/__pycache__/file_utils.cpython-311.pyc,,
fairseq/__pycache__/hub_utils.cpython-311.pyc,,
fairseq/__pycache__/incremental_decoding_utils.cpython-311.pyc,,
fairseq/__pycache__/iterative_refinement_generator.cpython-311.pyc,,
fairseq/__pycache__/nan_detector.cpython-311.pyc,,
fairseq/__pycache__/ngram_repeat_block.cpython-311.pyc,,
fairseq/__pycache__/options.cpython-311.pyc,,
fairseq/__pycache__/pdb.cpython-311.pyc,,
fairseq/__pycache__/quantization_utils.cpython-311.pyc,,
fairseq/__pycache__/registry.cpython-311.pyc,,
fairseq/__pycache__/search.cpython-311.pyc,,
fairseq/__pycache__/sequence_generator.cpython-311.pyc,,
fairseq/__pycache__/sequence_scorer.cpython-311.pyc,,
fairseq/__pycache__/speech_generator.cpython-311.pyc,,
fairseq/__pycache__/token_generation_constraints.cpython-311.pyc,,
fairseq/__pycache__/tokenizer.cpython-311.pyc,,
fairseq/__pycache__/trainer.cpython-311.pyc,,
fairseq/__pycache__/utils.cpython-311.pyc,,
fairseq/__pycache__/version.cpython-311.pyc,,
fairseq/benchmark/__init__.py,sha256=4X0hw-wOO99AdQcSEGQSXFa7aB5h9Eq0jF1U3wb2OnE,303
fairseq/benchmark/__pycache__/__init__.cpython-311.pyc,,
fairseq/benchmark/__pycache__/benchmark_multihead_attention.cpython-311.pyc,,
fairseq/benchmark/__pycache__/dummy_dataset.cpython-311.pyc,,
fairseq/benchmark/__pycache__/dummy_lm.cpython-311.pyc,,
fairseq/benchmark/__pycache__/dummy_masked_lm.cpython-311.pyc,,
fairseq/benchmark/__pycache__/dummy_model.cpython-311.pyc,,
fairseq/benchmark/__pycache__/dummy_mt.cpython-311.pyc,,
fairseq/benchmark/benchmark_multihead_attention.py,sha256=9HOiigRNmMrR66JhuQ9rNTvn7GHU5YSTTE3vkYv2yHA,4828
fairseq/benchmark/dummy_dataset.py,sha256=_IVDvGVSHOSsXXoBdU8Yn_lTxxbhhzsqD94bmBOS-V0,803
fairseq/benchmark/dummy_lm.py,sha256=HmmQVouthY_Io9nCtwO-_IbO5clZeg_EG1eHAtaRmYs,2757
fairseq/benchmark/dummy_masked_lm.py,sha256=Ql4PB0T8kYUGG8wcDlovQQjrgj8UtRtQSbm5HXTnUjM,3123
fairseq/benchmark/dummy_model.py,sha256=nzXkKH61U3fHIFfb8ZdP43e7F9vWw2e9J4LnOwDDfug,3090
fairseq/benchmark/dummy_mt.py,sha256=p83isyCUmev0ZWBLscDDzrsd7vda4P5L3sDi1uIO3nw,3677
fairseq/binarizer.py,sha256=tACLKdHLt1k0gopcumB1YAOsSkABdwaPKXNhqkUMeSg,11397
fairseq/checkpoint_utils.py,sha256=_fvv5e-QFrG0DhlhEOGofXQ886c5UnEXKbZnfqm1inE,35084
fairseq/config/__init__.py,sha256=3wjgyxS71cYZY22CWXn8ZHBa-mxU_5cLQBACfS6xzoM,177
fairseq/config/__pycache__/__init__.cpython-311.pyc,,
fairseq/config/config.yaml,sha256=Int4lyUmqFDhIBbnUHQtQ62zGt9R-M9oEd0SglKA-MY,308
fairseq/config/model/transformer_lm/transformer_lm_baevski_gbw.yaml,sha256=GQtOpWkYpt8C5slaYAOhCNEGuSKS1gGHtSjP_kpRF4g,991
fairseq/config/model/transformer_lm/transformer_lm_baevski_wiki103.yaml,sha256=4ArE4w2mTk1HXW_rc-p9Yuo3-xciktpgaZGHCXhFaBo,1010
fairseq/config/model/transformer_lm/transformer_lm_big.yaml,sha256=RK4gb2nauz8Lj9Aetgv_9mZPpK8bLWxIs7O4YDJtUk4,995
fairseq/config/model/transformer_lm/transformer_lm_gbw.yaml,sha256=GQtOpWkYpt8C5slaYAOhCNEGuSKS1gGHtSjP_kpRF4g,991
fairseq/config/model/transformer_lm/transformer_lm_gpt.yaml,sha256=GbN6h66LUVJlWcpON6BNnIhEJ-OegqaNND7dbJVjWtg,992
fairseq/config/model/transformer_lm/transformer_lm_gpt2_big.yaml,sha256=rQ6U9qNsj_jPDnveE69MiqjT77uxvGHJCvafMOML7rg,995
fairseq/config/model/transformer_lm/transformer_lm_gpt2_medium.yaml,sha256=oe5CpCgdEDz0zAvlMDo4s3Sj31Ek2b8cf_9YfadiOZc,995
fairseq/config/model/transformer_lm/transformer_lm_gpt2_small.yaml,sha256=1p443ChhD1rUpBhmCQl1JTOzpHFYnrN1EYjEMn6JXoY,995
fairseq/config/model/transformer_lm/transformer_lm_wiki103.yaml,sha256=4ArE4w2mTk1HXW_rc-p9Yuo3-xciktpgaZGHCXhFaBo,1010
fairseq/config/model/wav2vec/vq_wav2vec_gumbel.yaml,sha256=yMVZ1erYY6gDXVdfogFcx003Nh0ptsO08w7RDdy7JJk,85
fairseq/config/model/wav2vec2/wav2vec2_base.yaml,sha256=4NzpHmZBn5S0SVZFydh0XvV5Eyfu3LtAOY7rVnrKGrc,146
fairseq/config/model/wav2vec2/wav2vec2_large.yaml,sha256=Mkeigl2sdGe4qNCsVcVFGX5_9SS7svBvyu_5Rycz1pI,380
fairseq/criterions/__init__.py,sha256=CxzlSDIsWVqc3NeyZA6m1ZdYdf7MHgOR2oojRsMDJcU,997
fairseq/criterions/__pycache__/__init__.cpython-311.pyc,,
fairseq/criterions/__pycache__/adaptive_loss.cpython-311.pyc,,
fairseq/criterions/__pycache__/composite_loss.cpython-311.pyc,,
fairseq/criterions/__pycache__/cross_entropy.cpython-311.pyc,,
fairseq/criterions/__pycache__/ctc.cpython-311.pyc,,
fairseq/criterions/__pycache__/fairseq_criterion.cpython-311.pyc,,
fairseq/criterions/__pycache__/fastspeech2_loss.cpython-311.pyc,,
fairseq/criterions/__pycache__/hubert_criterion.cpython-311.pyc,,
fairseq/criterions/__pycache__/label_smoothed_cross_entropy.cpython-311.pyc,,
fairseq/criterions/__pycache__/label_smoothed_cross_entropy_latency_augmented.cpython-311.pyc,,
fairseq/criterions/__pycache__/label_smoothed_cross_entropy_with_alignment.cpython-311.pyc,,
fairseq/criterions/__pycache__/label_smoothed_cross_entropy_with_ctc.cpython-311.pyc,,
fairseq/criterions/__pycache__/legacy_masked_lm.cpython-311.pyc,,
fairseq/criterions/__pycache__/masked_lm.cpython-311.pyc,,
fairseq/criterions/__pycache__/model_criterion.cpython-311.pyc,,
fairseq/criterions/__pycache__/nat_loss.cpython-311.pyc,,
fairseq/criterions/__pycache__/sentence_prediction.cpython-311.pyc,,
fairseq/criterions/__pycache__/sentence_prediction_adapters.cpython-311.pyc,,
fairseq/criterions/__pycache__/sentence_ranking.cpython-311.pyc,,
fairseq/criterions/__pycache__/speech_to_speech_criterion.cpython-311.pyc,,
fairseq/criterions/__pycache__/speech_ulm_criterion.cpython-311.pyc,,
fairseq/criterions/__pycache__/tacotron2_loss.cpython-311.pyc,,
fairseq/criterions/__pycache__/wav2vec_criterion.cpython-311.pyc,,
fairseq/criterions/adaptive_loss.py,sha256=zb3ujq12utv70vt2If582Es4Hs1JehN_wkLUMdsnIeo,4558
fairseq/criterions/composite_loss.py,sha256=vNihG7mJiGuMW3W84NUZh7gYSlp5uLt_PsSPXlYnsSg,3793
fairseq/criterions/cross_entropy.py,sha256=0XmbaNnQjjgnbUsQPDKaSRcrlFK2zyBMrH-L1tSSgNM,3345
fairseq/criterions/ctc.py,sha256=j84YFBMtX7AuQW7VbmCo9E_MLw6Gm1Hrai9L33YVwFA,11095
fairseq/criterions/fairseq_criterion.py,sha256=h7dX-N4CK2HDsZnamhS9Vi1Nt3xZNUGtqLxVocNzCEU,4424
fairseq/criterions/fastspeech2_loss.py,sha256=yVtaq_zO8_CLI0gma8vFOqYIdXRXq3hS6j01K3QC8MQ,5323
fairseq/criterions/hubert_criterion.py,sha256=_PQhq4jZFTEGtTJHCE4I_dJEBbeJF2ZtDAZKKtpIL4c,7665
fairseq/criterions/label_smoothed_cross_entropy.py,sha256=-WaYNBxhKm9SFaTOQlAs-tLU3SDxOGKPlITj-vyFAk4,6264
fairseq/criterions/label_smoothed_cross_entropy_latency_augmented.py,sha256=fL8NHBATcuaJvnT1bWTEZFB2xhs0WBJCL9NGXNpeklU,7969
fairseq/criterions/label_smoothed_cross_entropy_with_alignment.py,sha256=sWaet1ShqbdaE4C9x6rusk9xpj-t7SVefDkROpOcrys,4748
fairseq/criterions/label_smoothed_cross_entropy_with_ctc.py,sha256=fUvFrY51eTjCMr-F3XeqAFeqSbICmYbc6kpZHOTyeVg,3376
fairseq/criterions/legacy_masked_lm.py,sha256=M76xXK5VM_COEjVqOdoKi5_6E2Z3Ayn6psnTSu_2suo,7006
fairseq/criterions/masked_lm.py,sha256=8re7NLH0mljxwGGQheH_uDvCWk1uEwSbaJrHc72eOh0,3402
fairseq/criterions/model_criterion.py,sha256=U0FF2Hsmj_Fc3QUqFIE6oLdNQ-ABOnPr0eEhuJ5-FX0,5380
fairseq/criterions/nat_loss.py,sha256=kXmuzDs1rffqkC3kqoBN7blpGDdvGdMDrG9sTBlIZA0,6355
fairseq/criterions/sentence_prediction.py,sha256=4zY5QJWjvY6QSPj6qOmeaz2qbIdHykFPcLsQvOXVUGY,5278
fairseq/criterions/sentence_prediction_adapters.py,sha256=XyBEP7-c6pd_I5ZbjL8Orb5XMsC_DpFtpgNktXc_pgk,2349
fairseq/criterions/sentence_ranking.py,sha256=5BUTs1LiBmohyuGrdSQgIO_4rKTeVLmB1lDlUVaGvm8,4614
fairseq/criterions/speech_to_speech_criterion.py,sha256=RVexfF_Sl8pFHIaQqSOg5ro5SK_zqT1rizZ4s38v5Ac,11657
fairseq/criterions/speech_ulm_criterion.py,sha256=VelfU6tPfBRrAYD9v6IUea2E3-Y_6LlfqwwORUxaJKA,4579
fairseq/criterions/tacotron2_loss.py,sha256=DhZ0r6VhvqIhyEClAoP1g1Mmb-rbTZWR1MZmFQOEzb8,8330
fairseq/criterions/wav2vec_criterion.py,sha256=J3VAfNl7kRsTFmn528jo96foRdsrMj5q26XrY2lIqMQ,8981
fairseq/data/__init__.py,sha256=repXERiCjt22ZTzV73RA-dZxqvgMTiQXrf598SChHq8,4430
fairseq/data/__pycache__/__init__.cpython-311.pyc,,
fairseq/data/__pycache__/add_target_dataset.cpython-311.pyc,,
fairseq/data/__pycache__/append_token_dataset.cpython-311.pyc,,
fairseq/data/__pycache__/backtranslation_dataset.cpython-311.pyc,,
fairseq/data/__pycache__/base_wrapper_dataset.cpython-311.pyc,,
fairseq/data/__pycache__/bucket_pad_length_dataset.cpython-311.pyc,,
fairseq/data/__pycache__/codedataset.cpython-311.pyc,,
fairseq/data/__pycache__/colorize_dataset.cpython-311.pyc,,
fairseq/data/__pycache__/concat_dataset.cpython-311.pyc,,
fairseq/data/__pycache__/concat_sentences_dataset.cpython-311.pyc,,
fairseq/data/__pycache__/data_utils.cpython-311.pyc,,
fairseq/data/__pycache__/denoising_dataset.cpython-311.pyc,,
fairseq/data/__pycache__/dictionary.cpython-311.pyc,,
fairseq/data/__pycache__/fairseq_dataset.cpython-311.pyc,,
fairseq/data/__pycache__/fasta_dataset.cpython-311.pyc,,
fairseq/data/__pycache__/id_dataset.cpython-311.pyc,,
fairseq/data/__pycache__/indexed_dataset.cpython-311.pyc,,
fairseq/data/__pycache__/iterators.cpython-311.pyc,,
fairseq/data/__pycache__/language_pair_dataset.cpython-311.pyc,,
fairseq/data/__pycache__/list_dataset.cpython-311.pyc,,
fairseq/data/__pycache__/lm_context_window_dataset.cpython-311.pyc,,
fairseq/data/__pycache__/lru_cache_dataset.cpython-311.pyc,,
fairseq/data/__pycache__/mask_tokens_dataset.cpython-311.pyc,,
fairseq/data/__pycache__/monolingual_dataset.cpython-311.pyc,,
fairseq/data/__pycache__/multi_corpus_dataset.cpython-311.pyc,,
fairseq/data/__pycache__/multi_corpus_sampled_dataset.cpython-311.pyc,,
fairseq/data/__pycache__/nested_dictionary_dataset.cpython-311.pyc,,
fairseq/data/__pycache__/noising.cpython-311.pyc,,
fairseq/data/__pycache__/num_samples_dataset.cpython-311.pyc,,
fairseq/data/__pycache__/numel_dataset.cpython-311.pyc,,
fairseq/data/__pycache__/offset_tokens_dataset.cpython-311.pyc,,
fairseq/data/__pycache__/pad_dataset.cpython-311.pyc,,
fairseq/data/__pycache__/plasma_utils.cpython-311.pyc,,
fairseq/data/__pycache__/prepend_dataset.cpython-311.pyc,,
fairseq/data/__pycache__/prepend_token_dataset.cpython-311.pyc,,
fairseq/data/__pycache__/raw_label_dataset.cpython-311.pyc,,
fairseq/data/__pycache__/replace_dataset.cpython-311.pyc,,
fairseq/data/__pycache__/resampling_dataset.cpython-311.pyc,,
fairseq/data/__pycache__/roll_dataset.cpython-311.pyc,,
fairseq/data/__pycache__/round_robin_zip_datasets.cpython-311.pyc,,
fairseq/data/__pycache__/shorten_dataset.cpython-311.pyc,,
fairseq/data/__pycache__/sort_dataset.cpython-311.pyc,,
fairseq/data/__pycache__/strip_token_dataset.cpython-311.pyc,,
fairseq/data/__pycache__/subsample_dataset.cpython-311.pyc,,
fairseq/data/__pycache__/text_compressor.cpython-311.pyc,,
fairseq/data/__pycache__/token_block_dataset.cpython-311.pyc,,
fairseq/data/__pycache__/transform_eos_concat_langpair_dataset.cpython-311.pyc,,
fairseq/data/__pycache__/transform_eos_dataset.cpython-311.pyc,,
fairseq/data/__pycache__/transform_eos_lang_pair_dataset.cpython-311.pyc,,
fairseq/data/add_target_dataset.py,sha256=XSdzYGi1UTrwBBTKWCTXieQ5aMPzS0KXS1tN1RqFAoM,2996
fairseq/data/append_token_dataset.py,sha256=OtPgZTJ9Jd_JIYhjLtRofrJsRz8VSDRhH4-AyjyJyXI,1065
fairseq/data/audio/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
fairseq/data/audio/__pycache__/__init__.cpython-311.pyc,,
fairseq/data/audio/__pycache__/audio_utils.cpython-311.pyc,,
fairseq/data/audio/__pycache__/data_cfg.cpython-311.pyc,,
fairseq/data/audio/__pycache__/frm_text_to_speech_dataset.cpython-311.pyc,,
fairseq/data/audio/__pycache__/hubert_dataset.cpython-311.pyc,,
fairseq/data/audio/__pycache__/multi_modality_dataset.cpython-311.pyc,,
fairseq/data/audio/__pycache__/raw_audio_dataset.cpython-311.pyc,,
fairseq/data/audio/__pycache__/speech_to_speech_dataset.cpython-311.pyc,,
fairseq/data/audio/__pycache__/speech_to_text_dataset.cpython-311.pyc,,
fairseq/data/audio/__pycache__/speech_to_text_joint_dataset.cpython-311.pyc,,
fairseq/data/audio/__pycache__/text_to_speech_dataset.cpython-311.pyc,,
fairseq/data/audio/audio_utils.py,sha256=V0-87ei4xFEayqqTGerIGGrwqmkzHeT3nAyIgp4NHt8,12438
fairseq/data/audio/data_cfg.py,sha256=qDbJSZcNoBZAAaXrVHoYU6OMLiQGG1eSuPvw8hcsHpw,10390
fairseq/data/audio/feature_transforms/__init__.py,sha256=qqZbvfwB5nHAUuZMvsM5wHpraCTAqqDJk25Xs15EBZo,2611
fairseq/data/audio/feature_transforms/__pycache__/__init__.cpython-311.pyc,,
fairseq/data/audio/feature_transforms/__pycache__/delta_deltas.cpython-311.pyc,,
fairseq/data/audio/feature_transforms/__pycache__/global_cmvn.cpython-311.pyc,,
fairseq/data/audio/feature_transforms/__pycache__/specaugment.cpython-311.pyc,,
fairseq/data/audio/feature_transforms/__pycache__/utterance_cmvn.cpython-311.pyc,,
fairseq/data/audio/feature_transforms/delta_deltas.py,sha256=1FhVpMdhQUMSzTVHb5E-SRmsCRA9FcBejCD8mHfXNEM,1194
fairseq/data/audio/feature_transforms/global_cmvn.py,sha256=z-xjI5aMe75FFT5PMqr7wfenYoTcS25Cb18yBGMnkpc,970
fairseq/data/audio/feature_transforms/specaugment.py,sha256=QFDIqTptCUk3sdoZff5dI3irTu8wGne57YJrBjVVbZA,4492
fairseq/data/audio/feature_transforms/utterance_cmvn.py,sha256=ZLO2zqozFnSMHeThKFxkoZPR9WTuNVf8Kmj9qaUkZMM,1186
fairseq/data/audio/frm_text_to_speech_dataset.py,sha256=IxyA9vZ13RWZbfEIW_odXlxhF_YdV0ZURu6AOTuvJq0,6923
fairseq/data/audio/hubert_dataset.py,sha256=-UIDY0Qx02pzhHEN7-VbdCq6GHEY50cHsKix-sJNYXU,12719
fairseq/data/audio/multi_modality_dataset.py,sha256=-RgsWF0Z2FeijBfRbwLcko7xO8B9tVWHiaeUhfxFTYw,9393
fairseq/data/audio/raw_audio_dataset.py,sha256=9rqPXFHzr-VZ1FNyadAbAcPrYhvEKuw32Klp4BfLZps,13679
fairseq/data/audio/speech_to_speech_dataset.py,sha256=O-nZmEkia4yXD8AvDqeLSm7d_go-WtU_Np1ZZ0OeYhI,15061
fairseq/data/audio/speech_to_text_dataset.py,sha256=5ml6d7z5wEfF9OK83rIA3U-2jsGWyu_wQxZ_Wwi5GWU,17401
fairseq/data/audio/speech_to_text_joint_dataset.py,sha256=sQi6o9wpqiOvvFoGG6zUcK-UvsJNRbHJQzcoEcLoFKY,12746
fairseq/data/audio/text_to_speech_dataset.py,sha256=DNFo-u1xC48SwAG3oLNGWIh3dccHdyiiR_5AZS6umHc,8621
fairseq/data/backtranslation_dataset.py,sha256=D8pex9lABk12HiuGPSB3Hiu2-cKmWafIcbd2Q2glKU4,6247
fairseq/data/base_wrapper_dataset.py,sha256=zbMCgcQx6GgiB-rwkofg-VPT-1qh52q3y5zTIW-o8j4,2153
fairseq/data/bucket_pad_length_dataset.py,sha256=y4RQN5mzxcK3f0jBXzUSAq497Hz5XUaVIkWReDP17d8,2360
fairseq/data/codedataset.py,sha256=x5yn23qeuDJta-4INzueCoXuPumrG9AyC9azku0t904,18486
fairseq/data/colorize_dataset.py,sha256=rdr-UdYyhB7symhBJfaqCYl3WvqIIKulxzR5sP_ez40,843
fairseq/data/concat_dataset.py,sha256=R3Da8O5Bx_iudtq_bVRxhmogrK1sML9GmTnmHOgql68,4645
fairseq/data/concat_sentences_dataset.py,sha256=MFdI5UzYWrsV6Xp2fN6hRLStj_Xg6lv0_d94c3mh8uU,1558
fairseq/data/data_utils.py,sha256=PP9J1TcrujbNPHtauIvdmbAXL0eQ5BcCK1cqqGlfkrg,21791
fairseq/data/data_utils_fast.cp311-win_amd64.pyd,sha256=IPbit8Vms-Io8Vc-XNgpDkP_UjCKKkqYnZ__4OB6UuA,157696
fairseq/data/denoising_dataset.py,sha256=HjRDcV2VtYMSQU8KO1arhlPtG7mjXlHZ55q6autjkkY,15627
fairseq/data/dictionary.py,sha256=PRSlqDlAHxFEztozHSandtr1xSL8S_VJ_mnChRxrfIA,12903
fairseq/data/encoders/__init__.py,sha256=z0S-AodtsWxkXd0aLl42KT0JxP9XL7ta6ybuyttSZy0,761
fairseq/data/encoders/__pycache__/__init__.cpython-311.pyc,,
fairseq/data/encoders/__pycache__/byte_bpe.cpython-311.pyc,,
fairseq/data/encoders/__pycache__/byte_utils.cpython-311.pyc,,
fairseq/data/encoders/__pycache__/bytes.cpython-311.pyc,,
fairseq/data/encoders/__pycache__/characters.cpython-311.pyc,,
fairseq/data/encoders/__pycache__/fastbpe.cpython-311.pyc,,
fairseq/data/encoders/__pycache__/gpt2_bpe.cpython-311.pyc,,
fairseq/data/encoders/__pycache__/gpt2_bpe_utils.cpython-311.pyc,,
fairseq/data/encoders/__pycache__/hf_bert_bpe.cpython-311.pyc,,
fairseq/data/encoders/__pycache__/hf_byte_bpe.cpython-311.pyc,,
fairseq/data/encoders/__pycache__/moses_tokenizer.cpython-311.pyc,,
fairseq/data/encoders/__pycache__/nltk_tokenizer.cpython-311.pyc,,
fairseq/data/encoders/__pycache__/sentencepiece_bpe.cpython-311.pyc,,
fairseq/data/encoders/__pycache__/space_tokenizer.cpython-311.pyc,,
fairseq/data/encoders/__pycache__/subword_nmt_bpe.cpython-311.pyc,,
fairseq/data/encoders/__pycache__/utils.cpython-311.pyc,,
fairseq/data/encoders/byte_bpe.py,sha256=fE7FWkW2hibm57PO3M-xZ2zkiaf6sUPi0yUv7mLAEeA,1404
fairseq/data/encoders/byte_utils.py,sha256=uPf8G9cblK8yqiTtr9V0K0m-6E8AmARFaeU4rACmPq4,1643
fairseq/data/encoders/bytes.py,sha256=inwcNa0E5aCXfLgWRx1DLkkiZV645uRtTh9ij-uEW1k,837
fairseq/data/encoders/characters.py,sha256=j6jf0gHBX8D3mb2aO0fVO8A9aAWMUs3mOYmAD9-DV-s,684
fairseq/data/encoders/fastbpe.py,sha256=pp8AuKK-4Sg81cPJ359CVA17RxtjVYy0uGdi8U-gFhA,1157
fairseq/data/encoders/gpt2_bpe.py,sha256=3yEVdK0u9Tv8mOAeNWA7dLWtrAV3SnLwrxchytmtJpI,1488
fairseq/data/encoders/gpt2_bpe_utils.py,sha256=Xny31QpN8tZw8T9jky9gy6bVX7E_t7FqV9JDcvGHgXk,4594
fairseq/data/encoders/hf_bert_bpe.py,sha256=fNJo7FsIHMngB5y1HX8JJ0BKXDU2vAWyBtZezOcYXAY,1642
fairseq/data/encoders/hf_byte_bpe.py,sha256=9noB26LlUoQ0Z-FZhaL2igGzKB5WfUp1ZQv7u5QCdl0,1710
fairseq/data/encoders/moses_tokenizer.py,sha256=oaUX6S4dom44ACeIZYKpfqITd7HgVIr6JvkqiBuQyak,1660
fairseq/data/encoders/nltk_tokenizer.py,sha256=MXxRoWDJzxdEd9G1eJLU9Ukd0fUNzIOFbdVmrnKO_l4,755
fairseq/data/encoders/sentencepiece_bpe.py,sha256=PpJBjdSNL4XVOcY2lwQQcJpGYXjdG1l-mR1nnk16xoE,2334
fairseq/data/encoders/space_tokenizer.py,sha256=UbzL4oOPA6RDO0lJ-8BqHq0zxmsvvQGOBUpWbxsQyUM,590
fairseq/data/encoders/subword_nmt_bpe.py,sha256=w73HSaQTBd1Aq1qEFCaaHFk9cBL3-7xxomXBtSgJsps,1791
fairseq/data/encoders/utils.py,sha256=0-2EO6Auz_ABpIMZO97cZEnIjJe-rDJJlws98KtTxaE,909
fairseq/data/fairseq_dataset.py,sha256=bXsW4q8xNHDYHJ3aCBIldtYYl7PDCyj-wCZObSexelo,7123
fairseq/data/fasta_dataset.py,sha256=5_chsbYH-zfyURv34jPJtt9jKdI-Baex8YYUEL6iKWc,3387
fairseq/data/huffman/__init__.py,sha256=u3opiBYA_ae_iJbICO7rUgk9oClV0EAjKm5VR7GrcFA,577
fairseq/data/huffman/__pycache__/__init__.cpython-311.pyc,,
fairseq/data/huffman/__pycache__/huffman_coder.cpython-311.pyc,,
fairseq/data/huffman/__pycache__/huffman_mmap_indexed_dataset.cpython-311.pyc,,
fairseq/data/huffman/huffman_coder.py,sha256=4u9kt1rs5fRIs0-L_YC8-4PSwXk6L_XcxiVr-qQn1ho,8627
fairseq/data/huffman/huffman_mmap_indexed_dataset.py,sha256=DMJmMenSLXJ7urZiJJrdqE2_StaBtMAEspzDT_Wam08,8807
fairseq/data/id_dataset.py,sha256=hR4OqL0sQ-PaQw5szBzcgX4Tjtw9p8Rymvgx-P5sjxc,423
fairseq/data/indexed_dataset.py,sha256=guKtamaXjgDAcBjv4-VrVW8KEc78t9l_9yxEduzhCLw,18265
fairseq/data/iterators.py,sha256=nkLcbRbsJa7OHsRb7OcY9PVKYXszjR7Cp_gEQ5mbv0A,31421
fairseq/data/language_pair_dataset.py,sha256=88Zg-VLV2ST7PIzkvPAoV_mwyrFljqNf5r1RDhKnELI,19102
fairseq/data/legacy/__init__.py,sha256=XG1IrbkO_ch-ketWVqMElsPQSXfEi1Ssg8v5NngdWI0,454
fairseq/data/legacy/__pycache__/__init__.cpython-311.pyc,,
fairseq/data/legacy/__pycache__/block_pair_dataset.cpython-311.pyc,,
fairseq/data/legacy/__pycache__/masked_lm_dataset.cpython-311.pyc,,
fairseq/data/legacy/__pycache__/masked_lm_dictionary.cpython-311.pyc,,
fairseq/data/legacy/block_pair_dataset.py,sha256=5eR4Nn5LPlXyEG0C1K53kN4D8tho0Y4XuQ4iwskvOWQ,12877
fairseq/data/legacy/masked_lm_dataset.py,sha256=91I1V8S-rK7vwixmOlPvtpkWtKbPlvj2YOYjwrLS4Xs,12168
fairseq/data/legacy/masked_lm_dictionary.py,sha256=Y_6ftxeLdldUzVtVVCPHrlhZqVdk13z9IdS5xZmI5Ko,1560
fairseq/data/list_dataset.py,sha256=IQsvK4lTXTrBZGDKfR5B2azUffEbq07BCmxvFmnxjkg,729
fairseq/data/lm_context_window_dataset.py,sha256=7XXNE77U_zjWizg_W6CyebwcknHXAqd1CmPU87MLGLQ,3381
fairseq/data/lru_cache_dataset.py,sha256=5A_ryKqZCo_m0rYn1mt1ctv1xidem6dCXYp1GkiwgaU,570
fairseq/data/mask_tokens_dataset.py,sha256=KwJrn44hW0JUUBmBJNcYUOIXkl_bYitTAYPIkDHZ_k0,8777
fairseq/data/monolingual_dataset.py,sha256=xaVtn6sIm3TGlM-igSQp9GHXW2eObdrQTUUSWtlu7x0,8832
fairseq/data/multi_corpus_dataset.py,sha256=Fju19afdlfdeU51nS9iMZP4fTdg4HMzO5WK6v7-S1yE,9359
fairseq/data/multi_corpus_sampled_dataset.py,sha256=8sv9wzQah262ZX9wXEBwo_FsKoDrk67hicGIKG7obb0,5311
fairseq/data/multilingual/__init__.py,sha256=3wjgyxS71cYZY22CWXn8ZHBa-mxU_5cLQBACfS6xzoM,177
fairseq/data/multilingual/__pycache__/__init__.cpython-311.pyc,,
fairseq/data/multilingual/__pycache__/multilingual_data_manager.cpython-311.pyc,,
fairseq/data/multilingual/__pycache__/multilingual_utils.cpython-311.pyc,,
fairseq/data/multilingual/__pycache__/sampled_multi_dataset.cpython-311.pyc,,
fairseq/data/multilingual/__pycache__/sampled_multi_epoch_dataset.cpython-311.pyc,,
fairseq/data/multilingual/__pycache__/sampling_method.cpython-311.pyc,,
fairseq/data/multilingual/multilingual_data_manager.py,sha256=pxiZGeu64XQmi1XdOOG0u96f87SVAsnMSce0N0dQxTM,44987
fairseq/data/multilingual/multilingual_utils.py,sha256=ouSWJyOLQ3GeADFIPBs-S9GS86jY5jLtISjNyJ-QxD8,1623
fairseq/data/multilingual/sampled_multi_dataset.py,sha256=3kJAPKt57pNO6MYztdH-oJNenABHTQe0MBgPcUFn0LM,18339
fairseq/data/multilingual/sampled_multi_epoch_dataset.py,sha256=IGQHUQcO-GgPohFwTYIsnjIesF7a0jDUJeaad76Ifbw,7823
fairseq/data/multilingual/sampling_method.py,sha256=2bo2g85278K2Z6HyAZeHD18noMZOkeKc3oKBpEPG4Uk,2068
fairseq/data/nested_dictionary_dataset.py,sha256=zKcBxd7IuNqD-j0W7Gvo6MDKAp5ydT1IYKC3iyMiqgM,4029
fairseq/data/noising.py,sha256=ktgrHaEjlIwhzNZKMkXaEnMiKES6JUXEigi8CLCv5Ak,12422
fairseq/data/num_samples_dataset.py,sha256=TXbL9mRMbvttqgDDL8RdqMMd7C3M0tgOA89ifNbm5o4,404
fairseq/data/numel_dataset.py,sha256=XeY9oKvEObOou5LtTeT3l_2MnD9tid04BhrdquaYdQk,786
fairseq/data/offset_tokens_dataset.py,sha256=7-XfSKfWdFmuAin9xdF2aYdZyiLRKVzlAtrIo3HpyL4,444
fairseq/data/pad_dataset.py,sha256=VjHXw-E7bY1PKjQ8Fl0-Hk9DjWwlM4AYwhtLnZb7ipc,941
fairseq/data/plasma_utils.py,sha256=3XZ6tnRZinthMwrkjqdr5_I2Wa-tLyRQfRoNaU3Sunc,6230
fairseq/data/prepend_dataset.py,sha256=Y4NCw77bu5t_3PsPpueymoklPqZYMYnO5K1UPIHB3ak,953
fairseq/data/prepend_token_dataset.py,sha256=kYAAoFViKbmSmeXelQb-Ee_fIoMh6FtuZLLaOD5Ox4g,1066
fairseq/data/raw_label_dataset.py,sha256=PaABzeon32ql5-7jIHFrN0ckl3gMBD9KDHmKbElfzT8,546
fairseq/data/replace_dataset.py,sha256=MoRmkqts8EkSTKTScegqqkR5Jx3P6JwuZrhEdIJzFbQ,1370
fairseq/data/resampling_dataset.py,sha256=Z-oWVjtBhJdxtypUz5c9Cde41MXEcPQ-IfgBx8JK6U4,4314
fairseq/data/roll_dataset.py,sha256=z3LU91-teGwbID52ohbZG8fkbxaZBNon4xrc4Qqh1_w,485
fairseq/data/round_robin_zip_datasets.py,sha256=TigeIQ_mFMMaE9f4Gf3BSDR1gmRxZ3Zg0NKk4icZLJI,6381
fairseq/data/shorten_dataset.py,sha256=d-ODM-IrtkzAzD9Y8k_MLWQdELpYypVYaxdSt8r_zIo,2443
fairseq/data/sort_dataset.py,sha256=jK4y8hg70UkgGnz8OQoETJfg1OdzFC5X8_YQU6XFdbw,621
fairseq/data/strip_token_dataset.py,sha256=ZjG_7LTDcyYmYciVbVFCAPumbhViHKR8PLOTMzlvNg0,647
fairseq/data/subsample_dataset.py,sha256=ZS-tojynsYzcF6HhHWoDuNTtbwr29bXzeo_b8og-HIY,2117
fairseq/data/text_compressor.py,sha256=-taXR4VIdPU3hCDrIvMFdfspJbRqrffeFLxgRqpy98E,1868
fairseq/data/token_block_dataset.py,sha256=AjUY3QHpBmI-2NeGd-87M2k9hsWqItRToZwJakjLalM,7652
fairseq/data/token_block_utils_fast.cp311-win_amd64.pyd,sha256=DZq2O4TZ1bBbHUuE7CXgPtO-QMWTZUWexa7rRtIQPpo,166400
fairseq/data/transform_eos_concat_langpair_dataset.py,sha256=24WRmFjieIsTwQQl4ftl1yR3edlAAoN2iEeFj1v_VCo,5126
fairseq/data/transform_eos_dataset.py,sha256=vXSeM-TdBXC7mB681q1cbgPI8jLlPNPX1Oph3wDnn5U,4575
fairseq/data/transform_eos_lang_pair_dataset.py,sha256=TVWOo2rcxtWj2KIUVmtPcTCWazmv8uWhqGdqgbptduQ,3856
fairseq/dataclass/__init__.py,sha256=Z2q2kGMUgmpKycztyfjQMm3b45AF8Mxf0gzScNN_HwI,308
fairseq/dataclass/__pycache__/__init__.cpython-311.pyc,,
fairseq/dataclass/__pycache__/configs.cpython-311.pyc,,
fairseq/dataclass/__pycache__/constants.cpython-311.pyc,,
fairseq/dataclass/__pycache__/initialize.cpython-311.pyc,,
fairseq/dataclass/__pycache__/utils.cpython-311.pyc,,
fairseq/dataclass/configs.py,sha256=O615ATVl20R37YWc6XeDl1iMnvrrfqrs7hkRzHQJ3eI,39421
fairseq/dataclass/constants.py,sha256=9ZCkZbRLKEFexwMX1b3gQw6YbNYXeXxmb5Z7rjSRcyc,1787
fairseq/dataclass/initialize.py,sha256=NbuOUGFJAq04XkZ_uk4lHHxSA_vTQ5jBl0pBbE3cctg,2057
fairseq/dataclass/utils.py,sha256=NeodzhTnbjQSoEVWpGvFouk9D_Y8yWJwvarmK_a5_rg,19010
fairseq/distributed/__init__.py,sha256=Nt4KSVbZFKYjCUFoItJieIYneQeCwFRxEj_Uz9Ene8I,775
fairseq/distributed/__pycache__/__init__.cpython-311.pyc,,
fairseq/distributed/__pycache__/distributed_timeout_wrapper.cpython-311.pyc,,
fairseq/distributed/__pycache__/fully_sharded_data_parallel.cpython-311.pyc,,
fairseq/distributed/__pycache__/legacy_distributed_data_parallel.cpython-311.pyc,,
fairseq/distributed/__pycache__/module_proxy_wrapper.cpython-311.pyc,,
fairseq/distributed/__pycache__/tpu_distributed_data_parallel.cpython-311.pyc,,
fairseq/distributed/__pycache__/utils.cpython-311.pyc,,
fairseq/distributed/distributed_timeout_wrapper.py,sha256=KMDUZH9ao4yRBqpEi94mi0Rmz9MkuFeqn_u6afjaVjw,3092
fairseq/distributed/fully_sharded_data_parallel.py,sha256=nUNdd2PcKYLqXc6A3euHBVV7gKdFHZr2jqVMrHqwvNU,4832
fairseq/distributed/legacy_distributed_data_parallel.py,sha256=8SNQIgU1pOHPIBT0wxISSExgGWgS5gSSUSX12W0xQqo,6102
fairseq/distributed/module_proxy_wrapper.py,sha256=vWjELHhb77BuURIPCkd6nJIWiY02hFvb0_UA3gSnYXI,1965
fairseq/distributed/tpu_distributed_data_parallel.py,sha256=ePg9acS3zipJnXfBngTPCjqhclfDR8iG1M9TQkmYO0o,1285
fairseq/distributed/utils.py,sha256=8B4H9_EsXoHJtuoXNeax3EXkPtbjD6xdw4RK880NYgU,29533
fairseq/examples/.gitignore,sha256=w-75VSfgBqvu8yyFEgo8lhb1JU-yoJybvBIdyN9HtPc,16
fairseq/examples/MMPT/.gitignore,sha256=-T-Fhu_JkPqbxUVjvMVsUwv9Jk1dlpwv19hKo_YJAPM,1920
fairseq/examples/MMPT/CONFIG.md,sha256=XCHqBR-0gHLeStWYtRFsV-J0z9cwaS72SaLHRHqH7QE,1746
fairseq/examples/MMPT/DATASET.md,sha256=3UiwGfw1761NSlRD4xm62etjMLK1Qj9DqLSTeJHiQiU,2629
fairseq/examples/MMPT/README.md,sha256=psEwuzAVSBT3PGlx595RX7RlsqLstCI74-uQCrQSFPU,9563
fairseq/examples/MMPT/__pycache__/locallaunch.cpython-311.pyc,,
fairseq/examples/MMPT/__pycache__/setup.cpython-311.pyc,,
fairseq/examples/MMPT/endtask.md,sha256=TQKTlAiKntpI51PyskPcXOf0v6d4gU8uT0a_TfeJ8YE,2452
fairseq/examples/MMPT/locallaunch.py,sha256=o1kYF_WWJ4R-vNhS464GHf1SQ8ghG_0RlBYYT8LM2lY,5336
fairseq/examples/MMPT/mmpt/__init__.py,sha256=nv_olffkPXVFTQURxWXq4O5rBVsoBGN3CMTiN8HfzVA,394
fairseq/examples/MMPT/mmpt/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/MMPT/mmpt/datasets/__init__.py,sha256=RS3N4O2L6sJQiEqkybGyzok1Cp04Ftc_q9D7Eg9aOIw,273
fairseq/examples/MMPT/mmpt/datasets/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/MMPT/mmpt/datasets/__pycache__/fairseqmmdataset.cpython-311.pyc,,
fairseq/examples/MMPT/mmpt/datasets/__pycache__/mmdataset.cpython-311.pyc,,
fairseq/examples/MMPT/mmpt/datasets/fairseqmmdataset.py,sha256=L7-Vtnkq3f9WGmSvx35U66U0OUPfct3Q8sIIt7rcK6Y,1785
fairseq/examples/MMPT/mmpt/datasets/mmdataset.py,sha256=tzkmZhikeevl-rT_KzhEcP6eEZa1rtlb7JoMJDxMn1o,3873
fairseq/examples/MMPT/mmpt/evaluators/__init__.py,sha256=qaOC5Y2ZYgxHS-rampjDZsBqTg4LCrwvxV3qQcby3tw,305
fairseq/examples/MMPT/mmpt/evaluators/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/MMPT/mmpt/evaluators/__pycache__/evaluator.cpython-311.pyc,,
fairseq/examples/MMPT/mmpt/evaluators/__pycache__/metric.cpython-311.pyc,,
fairseq/examples/MMPT/mmpt/evaluators/__pycache__/predictor.cpython-311.pyc,,
fairseq/examples/MMPT/mmpt/evaluators/evaluator.py,sha256=-yZThfvD5IDrs6SVA_tD9AV3KWOwGVZZUpi-Ogsb-Tk,2026
fairseq/examples/MMPT/mmpt/evaluators/metric.py,sha256=WgJHn_lwyfENZlQIFN3zRBsdvjBp7pSZck6WCSESsq0,10898
fairseq/examples/MMPT/mmpt/evaluators/predictor.py,sha256=UtT3TupSEmtv0c9doaX2hh4cqQiU24PtGE8_6i4EEjU,23125
fairseq/examples/MMPT/mmpt/losses/__init__.py,sha256=Dy0p_G9dyWM7KUWYCV1WDjfm9zlCX_ouDwMNdfX7aw4,345
fairseq/examples/MMPT/mmpt/losses/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/MMPT/mmpt/losses/__pycache__/fairseqmmloss.cpython-311.pyc,,
fairseq/examples/MMPT/mmpt/losses/__pycache__/loss.cpython-311.pyc,,
fairseq/examples/MMPT/mmpt/losses/__pycache__/nce.cpython-311.pyc,,
fairseq/examples/MMPT/mmpt/losses/fairseqmmloss.py,sha256=RejvD7h1iAqYgAL1g_PgzEp3yGowScde1W00O5oqNGE,2233
fairseq/examples/MMPT/mmpt/losses/loss.py,sha256=AeVxDcwVBzdgVAaGGYw_6BSbhWLzAIVMNCqPmxYLJi0,2095
fairseq/examples/MMPT/mmpt/losses/nce.py,sha256=PLPTEo5pED5fFq_vklzpbEZGs7CaUcumuMbxqL9S6uA,4586
fairseq/examples/MMPT/mmpt/models/__init__.py,sha256=C1mtYT4FK8gXp2FV_24vasnQqNGRcr5qdTKHlZrkBuw,395
fairseq/examples/MMPT/mmpt/models/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/MMPT/mmpt/models/__pycache__/fairseqmmmodel.cpython-311.pyc,,
fairseq/examples/MMPT/mmpt/models/__pycache__/mmfusion.cpython-311.pyc,,
fairseq/examples/MMPT/mmpt/models/__pycache__/mmfusionnlg.cpython-311.pyc,,
fairseq/examples/MMPT/mmpt/models/__pycache__/transformermodel.cpython-311.pyc,,
fairseq/examples/MMPT/mmpt/models/fairseqmmmodel.py,sha256=6sl1oAz7dIHDnwqGkulyW8G9gIL98zE_y6DLUKlJsHY,1417
fairseq/examples/MMPT/mmpt/models/mmfusion.py,sha256=vQnsrDw33doNCKLWgQJ5kJEKIxT22pMOpIQ7hAMRpqw,30634
fairseq/examples/MMPT/mmpt/models/mmfusionnlg.py,sha256=M4bvayOe5KNjhjtFFrbudSsrDD5M7qSmuZgi8UFLkGs,48394
fairseq/examples/MMPT/mmpt/models/transformermodel.py,sha256=m1PYhPn7fDZ09cinl716wHoU431sxgDUF85l4HSa6J0,26064
fairseq/examples/MMPT/mmpt/modules/__init__.py,sha256=4lTF98pma-9z72XTv25ERTsit2LOq7qFUwOuU827Hkw,255
fairseq/examples/MMPT/mmpt/modules/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/MMPT/mmpt/modules/__pycache__/mm.cpython-311.pyc,,
fairseq/examples/MMPT/mmpt/modules/__pycache__/retri.cpython-311.pyc,,
fairseq/examples/MMPT/mmpt/modules/__pycache__/vectorpool.cpython-311.pyc,,
fairseq/examples/MMPT/mmpt/modules/mm.py,sha256=wlefPwKHbbcJSNUby6EaHBGgLP7XLMQniDT0WMYCf0k,5537
fairseq/examples/MMPT/mmpt/modules/retri.py,sha256=de8NU41tQ47RKwKt956N6T3Y2TqSXAByH46ZCp2yQ00,15471
fairseq/examples/MMPT/mmpt/modules/vectorpool.py,sha256=g0njM3gnKZ45579yyVWl5vGwgSOzsiCIbUkExBJAFIQ,8278
fairseq/examples/MMPT/mmpt/processors/__init__.py,sha256=IuszbEglmi4WsiJgqARp1wioYlCveBEm2TPK3rlStRE,652
fairseq/examples/MMPT/mmpt/processors/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/MMPT/mmpt/processors/__pycache__/dedupprocessor.cpython-311.pyc,,
fairseq/examples/MMPT/mmpt/processors/__pycache__/dsprocessor.cpython-311.pyc,,
fairseq/examples/MMPT/mmpt/processors/__pycache__/how2processor.cpython-311.pyc,,
fairseq/examples/MMPT/mmpt/processors/__pycache__/how2retriprocessor.cpython-311.pyc,,
fairseq/examples/MMPT/mmpt/processors/__pycache__/processor.cpython-311.pyc,,
fairseq/examples/MMPT/mmpt/processors/dedupprocessor.py,sha256=pj_fPvUyLtBi-UvZWAkrS7-tehmtaZ4ch53Ce6xvq_g,8834
fairseq/examples/MMPT/mmpt/processors/dsprocessor.py,sha256=V-TAMxzS2SJe9BXbDGwFqCuhF5hPzMD4Jxk1CqwEYSU,29891
fairseq/examples/MMPT/mmpt/processors/how2processor.py,sha256=EenbvOQR46sU2aMuC9lmqlUhJDY4EhnUNXbIfbm3QXQ,32302
fairseq/examples/MMPT/mmpt/processors/how2retriprocessor.py,sha256=G-sRiU5GOdco6X9gtAXJ4qI1WwEuvJ28msfm6Fz_X-g,3742
fairseq/examples/MMPT/mmpt/processors/models/__pycache__/s3dg.cpython-311.pyc,,
fairseq/examples/MMPT/mmpt/processors/models/s3dg.py,sha256=Do21vhjCyR_dhjCly21nJHi13ftShfweXunMOPLiEQk,12416
fairseq/examples/MMPT/mmpt/processors/processor.py,sha256=bdS03KfHtV5O08q_x1rPjRvwikubCmi39-VePDi4-SE,9358
fairseq/examples/MMPT/mmpt/tasks/__init__.py,sha256=zmDT2TUX49R9BFUhrtHDx31BO-a1IsvXIODP7FEisIw,445
fairseq/examples/MMPT/mmpt/tasks/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/MMPT/mmpt/tasks/__pycache__/fairseqmmtask.cpython-311.pyc,,
fairseq/examples/MMPT/mmpt/tasks/__pycache__/milncetask.cpython-311.pyc,,
fairseq/examples/MMPT/mmpt/tasks/__pycache__/retritask.cpython-311.pyc,,
fairseq/examples/MMPT/mmpt/tasks/__pycache__/task.cpython-311.pyc,,
fairseq/examples/MMPT/mmpt/tasks/__pycache__/vlmtask.cpython-311.pyc,,
fairseq/examples/MMPT/mmpt/tasks/fairseqmmtask.py,sha256=N2HXlN0HPav03XZPnxDp8kk8_9F6MkU0nDpv-xAJeSo,3045
fairseq/examples/MMPT/mmpt/tasks/milncetask.py,sha256=kTyxugAOTEY6dxKMANLosWRv3dW7HvzzVgTP5FS00M4,954
fairseq/examples/MMPT/mmpt/tasks/retritask.py,sha256=LNEbUWTUCeryX99incdzK1D5KHJSfo1XsP21oo3GEr0,8413
fairseq/examples/MMPT/mmpt/tasks/task.py,sha256=Eo6MzpgB9Sz2L-d_W7A_TZsAFnaWiHrLuopG5cx7Se0,6780
fairseq/examples/MMPT/mmpt/tasks/vlmtask.py,sha256=705AtyRpWv15ashL2giF477FDAxXlmbGxX8K_2FFLcQ,856
fairseq/examples/MMPT/mmpt/utils/__init__.py,sha256=LuBn9APshIU4Qt6ycj6rHvuEohiOvZBpsATz-_PLazo,1886
fairseq/examples/MMPT/mmpt/utils/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/MMPT/mmpt/utils/__pycache__/load_config.cpython-311.pyc,,
fairseq/examples/MMPT/mmpt/utils/__pycache__/shardedtensor.cpython-311.pyc,,
fairseq/examples/MMPT/mmpt/utils/load_config.py,sha256=UHcy6AULa3LOCj56itnOECswPGs0mKrJuehM9utUZyc,3155
fairseq/examples/MMPT/mmpt/utils/shardedtensor.py,sha256=EkPou4E2d91q4dx61ib4GYsED2m4-m-nyNibTeUQQG0,1410
fairseq/examples/MMPT/mmpt_cli/__pycache__/localjob.cpython-311.pyc,,
fairseq/examples/MMPT/mmpt_cli/__pycache__/predict.cpython-311.pyc,,
fairseq/examples/MMPT/mmpt_cli/localjob.py,sha256=fJzlUIhNbsWDWtrnPIPNjuTRngkkZaQQ17U0bSFWOSE,3794
fairseq/examples/MMPT/mmpt_cli/predict.py,sha256=KRJDcXuZlnHbb9LhHpZhqH8YvdHw7vBh8uPu2V1AkeQ,3937
fairseq/examples/MMPT/pretraining.md,sha256=_6dUENPX_vrgVgbeits5knSdsFLfEX1h_wy8Snq0e2I,1884
fairseq/examples/MMPT/projects/mfmmlm.yaml,sha256=qXrDZZ9_CRy_lqoW7TL0c7xNri85HnuOrR1isMunEu8,1321
fairseq/examples/MMPT/projects/mtm/mmfusionmtm.yaml,sha256=MoXgXYV95ldIeRvsONKmtO8zcAGhrtbSzjo4Cf5ZUS8,430
fairseq/examples/MMPT/projects/mtm/vlm.yaml,sha256=_eFD5ohiXjT4-YgRQzfhU9CnZNI6cISNzHZ4CnTVT2s,153
fairseq/examples/MMPT/projects/mtm/vlm/coin.yaml,sha256=O1YKqQNtSo1w3ffbYDantYZclkKz5s-EZpM2f19keXc,1155
fairseq/examples/MMPT/projects/mtm/vlm/crosstask.yaml,sha256=m3XVXAu02-UOT6oZ8Kxe_dJbW1eZqheb1e7n3PxcFUg,1502
fairseq/examples/MMPT/projects/mtm/vlm/how2.yaml,sha256=VU1eJVhizF8kO4jIxaPNRHey2Xnx7dquJMhvmmknV7A,1302
fairseq/examples/MMPT/projects/mtm/vlm/test_coin.yaml,sha256=HBCE8o9uJuwOoHnVW6oG0CgKg-6CYTyAjNTD1FOBI6A,811
fairseq/examples/MMPT/projects/mtm/vlm/test_crosstask.yaml,sha256=2ATUIglhQkzuNEQKYrCI8_zuDjKDnr0e3qHuf-qW-8g,1200
fairseq/examples/MMPT/projects/mtm/vlm/test_crosstask_zs.yaml,sha256=zo9MwGXDsv8CcskJP1AB2B4wRoDiZlRZ73s-Rhg6f-A,1193
fairseq/examples/MMPT/projects/mtm/vlm/test_vtt.yaml,sha256=g6bOpa4SinUJWxNClP03K11neTzX9DwRFAvzSWs1RTM,693
fairseq/examples/MMPT/projects/mtm/vlm/test_vttqa.yaml,sha256=L0kZe5tJpxMpAcaWk2YjKvsXofrAWgzFlfNCD_MRfc4,687
fairseq/examples/MMPT/projects/mtm/vlm/test_youcook.yaml,sha256=EeUhQtuzODMnDQ2qtFoVGmegsV05eFSRp5cR_yyLkJI,799
fairseq/examples/MMPT/projects/mtm/vlm/test_youcookcap.yaml,sha256=ifVJKKdDH4URpFoKXKkWR30U2EdIuRd9WyX0eo5uV3E,797
fairseq/examples/MMPT/projects/mtm/vlm/vtt.yaml,sha256=ewrYvp30ti8vJeY-X-d9nQ48htnFI-0fxjj3faveTdw,1217
fairseq/examples/MMPT/projects/mtm/vlm/vttqa.yaml,sha256=LwZaV9Fq2m_uDMf_dZWxDu9SlVCKRzF6Xpe1ZWbgeuM,1113
fairseq/examples/MMPT/projects/mtm/vlm/youcook.yaml,sha256=npWz7ud6yyza0UVx9NfLHqxbWv3AvmXUlBo3QqmTQXM,1164
fairseq/examples/MMPT/projects/mtm/vlm/youcookcap.yaml,sha256=VHogGvzTFQ7htV2J0aV9iarbdow-URsrhsSXovwyWqM,1112
fairseq/examples/MMPT/projects/retri/videoclip.yaml,sha256=yo5_ugv-9vSf61thPl2ToiBhXVW0_37aqMn3t9JtIAU,271
fairseq/examples/MMPT/projects/retri/videoclip/coin_videoclip.yaml,sha256=IPrUltWGafTiNS2DUDucF8pQBezK0nZTapW4OHs-E8o,1244
fairseq/examples/MMPT/projects/retri/videoclip/crosstask_videoclip.yaml,sha256=c58B-8ADAkEJoSex54vhQ6Qvuf7sgAUv81RCsRieemY,1596
fairseq/examples/MMPT/projects/retri/videoclip/how2.yaml,sha256=Cn4L2xqDA0CxSr_tJdhxRNWfjr_Q1r5HqyFCSb0Xnto,1640
fairseq/examples/MMPT/projects/retri/videoclip/test_coin_videoclip.yaml,sha256=1vCadq_hGkD4BiNwfXPnOb5ZgJRB2WbmO3f6SfxW0hM,900
fairseq/examples/MMPT/projects/retri/videoclip/test_coin_zs.yaml,sha256=qcGg3rTTe7TLxuKmjqkdWSrspKZgDuESJ1qh8BclUJw,870
fairseq/examples/MMPT/projects/retri/videoclip/test_crosstask_videoclip.yaml,sha256=8qGVllbYT1n71CoT_7nTvVuybFoufo6sDZOQNMgkZaM,1291
fairseq/examples/MMPT/projects/retri/videoclip/test_crosstask_zs_videoclip.yaml,sha256=zXnIrq7GFCpD6mRIc2NhpMBe2YicMSBXQ1Zo-T3asSk,1284
fairseq/examples/MMPT/projects/retri/videoclip/test_didemo_zs.yaml,sha256=21x0xbAHwdts594AZif9NkAogw_aHbgKrI0vAV8dONk,772
fairseq/examples/MMPT/projects/retri/videoclip/test_vtt_videoclip.yaml,sha256=IjZv1m6OkWtYQ7khgj4VDhOiT_Ko-bFNQ4hdy2VWeiQ,779
fairseq/examples/MMPT/projects/retri/videoclip/test_vtt_zs.yaml,sha256=yjiaTHSote1w4dQYGnOWvhc62cBkCa2ovno4Ky5Q3b4,778
fairseq/examples/MMPT/projects/retri/videoclip/test_vttqa_videoclip.yaml,sha256=FlHbKlxrFS-2ojWC3ap82dTq6rgZX2xTCIdJtbN4-fI,773
fairseq/examples/MMPT/projects/retri/videoclip/test_vttqa_zs.yaml,sha256=2n6GgJ2ZqoxkzBaahYhq1MKBXXpbLmoF9g86IdHvGJA,770
fairseq/examples/MMPT/projects/retri/videoclip/test_youcook_videoclip.yaml,sha256=3Ti8dixDGtUyQr0d2793LVL3zPrLOM5OYFXVDceHlh0,885
fairseq/examples/MMPT/projects/retri/videoclip/test_youcook_zs.yaml,sha256=CrIOAqgEJatdFnAdb7Hf5Ngy8vIZ6nj_0WUt9GF_f0o,880
fairseq/examples/MMPT/projects/retri/videoclip/vtt_videoclip.yaml,sha256=qd4pgtWnl5ohaUgM5wX4RN8i4SIurX2_cEjn88RNuww,1303
fairseq/examples/MMPT/projects/retri/videoclip/vttqa_videoclip.yaml,sha256=3gbp7RAU7Y-G87G-bZZe08vckD4sf0HmYCovIORJ-Uo,1199
fairseq/examples/MMPT/projects/retri/videoclip/youcook_videoclip.yaml,sha256=n5rlyWGVLYshpjYsA-rY6nJOL5kM0vCbXYRNychEwQA,1250
fairseq/examples/MMPT/projects/retri/videoretri.yaml,sha256=MkbY7MvI8i5XQB0Bf4QRSNIrZcmebuEt1xR9FCNvmTw,1514
fairseq/examples/MMPT/projects/task/coin.yaml,sha256=6OkeSd7iysrmBceALnIt6T-Dt4HuxNZPJAPmQk5vkgw,653
fairseq/examples/MMPT/projects/task/coin_videoclip.yaml,sha256=nFnxc6Qtq2hDXAyCLwbo4yuGtC_k8xkB8riRu9HKoJU,237
fairseq/examples/MMPT/projects/task/crosstask.yaml,sha256=Ff5c3ligBvrpJXIV0tdd0dt8Z2YZoPLuFX6lsNU2kCg,1057
fairseq/examples/MMPT/projects/task/crosstask_videoclip.yaml,sha256=BdM7ExCZ1qbWnYOMpeUCWhiEgh_yElWGQ7waasFUZbU,333
fairseq/examples/MMPT/projects/task/default.yaml,sha256=T_Vxh72taH5TZjj_q6a9_ZHwpdU3yOe2W_CNSi812YQ,573
fairseq/examples/MMPT/projects/task/ft.yaml,sha256=gmdvb4cNk-nJGita-p1X4mieDK8teZe5YelSlAeYmGE,473
fairseq/examples/MMPT/projects/task/how2.yaml,sha256=yqJYOoqBOHYboHjZkH0paw1rUb0zvcPWeFuyX8pzFUc,653
fairseq/examples/MMPT/projects/task/test.yaml,sha256=CGCdAq-vIvvNlXAcONQ-tH5FTUNKKEc9yCejQpdkwXg,300
fairseq/examples/MMPT/projects/task/test_coin.yaml,sha256=fv4SN94dKyEwPYnjJTl_Hnm1b5wrd7oS7StSMiklVuY,669
fairseq/examples/MMPT/projects/task/test_coin_videoclip.yaml,sha256=yR8hZXGWTE5PEfdFdmraCD12O5HWU1ltT8LHpi5tMPE,242
fairseq/examples/MMPT/projects/task/test_coin_zs.yaml,sha256=6SFYZgBjQqRo71JmHkdQ0fVAo-NvgV78gj0eFCHFEvY,324
fairseq/examples/MMPT/projects/task/test_crosstask.yaml,sha256=G7oXkf0QBiAJijprTkhYevkjXbRYxQVo5sHyowOWzuM,1122
fairseq/examples/MMPT/projects/task/test_crosstask_videoclip.yaml,sha256=bxzx9jgQbzEu9-YhbJmsAqJCs-vbjrI6l115VVeX7Wc,235
fairseq/examples/MMPT/projects/task/test_crosstask_zs.yaml,sha256=E8pP95nVJGz_mK4uJvM63TmvU1axQuHj2hPx9pBbAwQ,1188
fairseq/examples/MMPT/projects/task/test_crosstask_zs_videoclip.yaml,sha256=_bYfxjmUedlx8-lkVvDq7vUrCaLE1PzBbrWCLjC03ck,238
fairseq/examples/MMPT/projects/task/test_didemo_zs.yaml,sha256=B-02dD05av85ezeo7mSgrtLCoyFDNg3Hvzn4fDPza_s,636
fairseq/examples/MMPT/projects/task/test_vtt.yaml,sha256=xPg2T77yN5r-kAW51yqVTuouIP9kndJcWDF4tVywAUo,536
fairseq/examples/MMPT/projects/task/test_vtt_videoclip.yaml,sha256=E-cjc0C2IbCqPaRcQtuacuV2BaSm7j3boWF7Hpn2kuQ,192
fairseq/examples/MMPT/projects/task/test_vtt_zs.yaml,sha256=Gd4EAsOovGH-y07AMB7GHp3hhV2G-aTrAdQrkqTdgZE,346
fairseq/examples/MMPT/projects/task/test_vttqa.yaml,sha256=pQN5OV5AvZLnMp78OJLUkoMt767TQyOQrpgr8f4Dud4,551
fairseq/examples/MMPT/projects/task/test_vttqa_videoclip.yaml,sha256=qvHLswIdNbBfyq9BnoRpOQkKylXn978mo4GrqR3mvmM,194
fairseq/examples/MMPT/projects/task/test_vttqa_zs.yaml,sha256=gJmXDVWpF7AqU5f1CX_N0AFESvjew4O_uKky539IQkM,350
fairseq/examples/MMPT/projects/task/test_youcook.yaml,sha256=KZeuVXZSkDRROSW_wAGonimrxQi7EaDm5REKFLQs9RY,750
fairseq/examples/MMPT/projects/task/test_youcook_videoclip.yaml,sha256=XpYHU0LxNnDUvesGKyerAa3VyqejJAZSxLSyhCE1-_8,196
fairseq/examples/MMPT/projects/task/test_youcook_zs.yaml,sha256=CQBGH_ibprkEJNTaRQfY4jmc_fAZz_fygeoieUeENLA,354
fairseq/examples/MMPT/projects/task/test_youcookcap.yaml,sha256=3DYfgQizsiZKmcDF66NRFfbfcqMI8Cig8WOWt_Jz16E,661
fairseq/examples/MMPT/projects/task/vtt.yaml,sha256=37dj1c2SETeixwxpVJ2iKLMLFx8m7xME4cC4lYnNkRk,658
fairseq/examples/MMPT/projects/task/vtt_videoclip.yaml,sha256=B8yUgGmS_HCzB8hwgFk43MVjxQiCIGY_Vz_crfGsBsE,292
fairseq/examples/MMPT/projects/task/vttqa.yaml,sha256=YtQaN_PlLHqIlcQNtHMrHAZJZTQlkmLX5vYAEO_miqA,554
fairseq/examples/MMPT/projects/task/vttqa_videoclip.yaml,sha256=xIjt1vmJyWVZgxkoR96-OoeSO5AL6bCzQiEkBqCjURg,255
fairseq/examples/MMPT/projects/task/youcook.yaml,sha256=TE-uOWhMoH1iUohmPivEyoidq0OAPyMTBiSki5aAjRI,728
fairseq/examples/MMPT/projects/task/youcook_videoclip.yaml,sha256=QK4dry8P6NMbVBIHnYmLJTMnUcKwrTOBwsNGv7OBrhQ,256
fairseq/examples/MMPT/projects/task/youcookcap.yaml,sha256=5NjlVWoLKAXjPPLEwIORZtzNKdNxkrsLY7QXewYh4ek,624
fairseq/examples/MMPT/scripts/text_token_extractor/__pycache__/pretokenization.cpython-311.pyc,,
fairseq/examples/MMPT/scripts/text_token_extractor/configs/bert-base-uncased.yaml,sha256=XpY7WNYGXH_y61ywTkM7DXNa-A0zVPwcp2miEJ7jXe4,159
fairseq/examples/MMPT/scripts/text_token_extractor/pretokenization.py,sha256=oHCHijE3iMsSno9gvRZotyrUrvbGv26e9zoenhqYfyM,3408
fairseq/examples/MMPT/scripts/video_feature_extractor/__pycache__/extract.cpython-311.pyc,,
fairseq/examples/MMPT/scripts/video_feature_extractor/__pycache__/model.cpython-311.pyc,,
fairseq/examples/MMPT/scripts/video_feature_extractor/__pycache__/pathbuilder.cpython-311.pyc,,
fairseq/examples/MMPT/scripts/video_feature_extractor/__pycache__/preprocessing.cpython-311.pyc,,
fairseq/examples/MMPT/scripts/video_feature_extractor/__pycache__/random_sequence_shuffler.cpython-311.pyc,,
fairseq/examples/MMPT/scripts/video_feature_extractor/__pycache__/shard_feature.cpython-311.pyc,,
fairseq/examples/MMPT/scripts/video_feature_extractor/__pycache__/videoreader.cpython-311.pyc,,
fairseq/examples/MMPT/scripts/video_feature_extractor/extract.py,sha256=TI2NpV9DK-ORXXyLmDS5Chf5J6zburypybhZNH2A1OY,5529
fairseq/examples/MMPT/scripts/video_feature_extractor/how2/s3d.sh,sha256=LylOqLptYV9v08Qm8ElxCvpM5DlkvqcjanHUZPNOFek,219
fairseq/examples/MMPT/scripts/video_feature_extractor/model.py,sha256=T4sYRhmEitzTqfDz_TxQsOU81RhmYXDIDYM8YIUtep0,1921
fairseq/examples/MMPT/scripts/video_feature_extractor/pathbuilder.py,sha256=TDwo29mcBawcemuVTBygnt-Sf0gt7QdhEaDWyE6JaFI,3410
fairseq/examples/MMPT/scripts/video_feature_extractor/preprocessing.py,sha256=p-ELb3rjGt4NSq3PhmPIaxEijxf2scM6Qjw6_u0uu0s,2071
fairseq/examples/MMPT/scripts/video_feature_extractor/random_sequence_shuffler.py,sha256=qyuUWIJapi7maPHRbjgAEHDgFcihP13dWAUegBdcBm4,829
fairseq/examples/MMPT/scripts/video_feature_extractor/shard_feature.py,sha256=Lay1JYOp2S8VdUUiriGqr1OxhhjChv-KX7kzdk__UmE,2166
fairseq/examples/MMPT/scripts/video_feature_extractor/videoreader.py,sha256=1DOKOwP_Q1U4H5FJkWc4APjV9UTdAORzQ2KsvwDOyRc,8322
fairseq/examples/MMPT/setup.py,sha256=4da_MFdPTpsXrvFY2b1cMf8tYdSV1DtZic-lTlDXdRo,668
fairseq/examples/MMPT/videoclip.png,sha256=HVT-GNElmt6TMueP23T4NP372wsEhlF-anzUiVazBmM,385871
fairseq/examples/MMPT/vlm.png,sha256=cihS7WJYrJ9__T45E_oaNwcCxNmJ722IGEdDLVmt5OU,418405
fairseq/examples/__init__.py,sha256=oxVA6_tUoKKOMY0Jz6_12sRb5dFt0NjINSHPcWnGHzw,264
fairseq/examples/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/adaptive_span/README.md,sha256=ny-YwXWVrM9QVWFGpU74EFQb0mE_Rvz1-CEyPuPNQDQ,4375
fairseq/examples/adaptive_span/__init__.py,sha256=857EoGW73n-Gfmg7pJCn4wGSSNbIV0Tovl_rvLFe6ck,669
fairseq/examples/adaptive_span/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/adaptive_span/__pycache__/adagrad_with_grad_clip.cpython-311.pyc,,
fairseq/examples/adaptive_span/__pycache__/adaptive_span_attention.cpython-311.pyc,,
fairseq/examples/adaptive_span/__pycache__/adaptive_span_loss.cpython-311.pyc,,
fairseq/examples/adaptive_span/__pycache__/adaptive_span_model.cpython-311.pyc,,
fairseq/examples/adaptive_span/__pycache__/adaptive_span_model_wrapper.cpython-311.pyc,,
fairseq/examples/adaptive_span/__pycache__/truncated_bptt_lm_task.cpython-311.pyc,,
fairseq/examples/adaptive_span/adagrad_with_grad_clip.py,sha256=JHn8Bm14GWxtpi1t6qHpmhr7-qTykgLNRjXes_HstLo,4374
fairseq/examples/adaptive_span/adaptive_span_attention.py,sha256=qrhpL8PAII-7vHplZ4dV64nVVPgaR7YsnbM2AVoWmV8,5881
fairseq/examples/adaptive_span/adaptive_span_loss.py,sha256=5pRr5zS_yVyFU_OtM5SMY-Y_OBnKX7zINpCIOWu96Lc,4233
fairseq/examples/adaptive_span/adaptive_span_model.py,sha256=w3CubYPD_rJMMMrArQ0g8SgLkiOOKM5rsDEv-3knK8k,8540
fairseq/examples/adaptive_span/adaptive_span_model_wrapper.py,sha256=PXOM_OF7gDk3OpKsXs_UgohT4mg9-0vBMWkeR9crse0,4692
fairseq/examples/adaptive_span/truncated_bptt_lm_task.py,sha256=EWnIkcc_HK3FvtWLkKD__3gtfhuZFqV51YVL7UXx1do,9995
fairseq/examples/attention_head_selection/README.md,sha256=_kP-1exwR7M51CmYCNqKKHIzh2g3w9NbH_NRlkPaD94,6814
fairseq/examples/attention_head_selection/src/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
fairseq/examples/attention_head_selection/src/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/attention_head_selection/src/__pycache__/speech_to_text_head_selection.cpython-311.pyc,,
fairseq/examples/attention_head_selection/src/data/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
fairseq/examples/attention_head_selection/src/data/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/attention_head_selection/src/data/__pycache__/speech_to_text_dataset_with_domain.cpython-311.pyc,,
fairseq/examples/attention_head_selection/src/data/speech_to_text_dataset_with_domain.py,sha256=ljAv502xtnH3lC5VAxD-79clx7epEIi77-OfSAgB40k,8439
fairseq/examples/attention_head_selection/src/loss/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
fairseq/examples/attention_head_selection/src/loss/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/attention_head_selection/src/loss/__pycache__/attention_head_selection.cpython-311.pyc,,
fairseq/examples/attention_head_selection/src/loss/attention_head_selection.py,sha256=foFBKQQtW_V4uZ_qr5d8pFk-FF12NMYvUDly7vjJn7A,872
fairseq/examples/attention_head_selection/src/models/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
fairseq/examples/attention_head_selection/src/models/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/attention_head_selection/src/models/__pycache__/head_selection_s2t_transformer.cpython-311.pyc,,
fairseq/examples/attention_head_selection/src/models/__pycache__/head_selection_transformer.cpython-311.pyc,,
fairseq/examples/attention_head_selection/src/models/head_selection_s2t_transformer.py,sha256=_QHfESHjUA2pP9s7l633pe-wU1uiFUD_8WBTfmvY5E0,6831
fairseq/examples/attention_head_selection/src/models/head_selection_transformer.py,sha256=wgL3rnUdlm5lHxMV-x5vdbfFO7fCQsFE5-n4Swp6cSU,7637
fairseq/examples/attention_head_selection/src/modules/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
fairseq/examples/attention_head_selection/src/modules/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/attention_head_selection/src/modules/__pycache__/attn_head_selector.cpython-311.pyc,,
fairseq/examples/attention_head_selection/src/modules/__pycache__/head_selection_transformer_layer.cpython-311.pyc,,
fairseq/examples/attention_head_selection/src/modules/__pycache__/multihead_attention_selection.cpython-311.pyc,,
fairseq/examples/attention_head_selection/src/modules/__pycache__/multihead_functional.cpython-311.pyc,,
fairseq/examples/attention_head_selection/src/modules/attn_head_selector.py,sha256=YffWWNVJL7JcT2dGlTKLuB8I-_tAxZaPMnS8oi8EVa4,3074
fairseq/examples/attention_head_selection/src/modules/head_selection_transformer_layer.py,sha256=lGOAabSPuFs-2kLmsAyU2OetKVB82ULLloa86fgssVg,3509
fairseq/examples/attention_head_selection/src/modules/multihead_attention_selection.py,sha256=T-_LvTBLluPDHDly_6wsMfwNdKz1RSsNmF7HvabFFRg,14046
fairseq/examples/attention_head_selection/src/modules/multihead_functional.py,sha256=ljW8E5BIQ383RXjvdCToNJwdWk7kN1SZxilY_KW5mHw,11418
fairseq/examples/attention_head_selection/src/speech_to_text_head_selection.py,sha256=1Ij5gmf5ylvCthRcIU5XNFHW2-yWJ0j6WiMIt1YqAB4,7727
fairseq/examples/backtranslation/README.md,sha256=vrs9cAFaroyNYtCVMYDTGeto5JWl66kcZjojOm0HO-A,10775
fairseq/examples/backtranslation/__pycache__/deduplicate_lines.cpython-311.pyc,,
fairseq/examples/backtranslation/__pycache__/extract_bt_data.cpython-311.pyc,,
fairseq/examples/backtranslation/deduplicate_lines.py,sha256=WN7w0dnFG65X2G7xoJMt_rtWE7sTDImVMQpq2IqliuM,1221
fairseq/examples/backtranslation/extract_bt_data.py,sha256=ibdN-cPNZ6VTdTsDMjB63TRegrabWpOaGnL549vdsXA,2509
fairseq/examples/backtranslation/prepare-de-monolingual.sh,sha256=TPn8vYzFV2u4BSqUqjWWQQwi-J6K6S-nugWksnPNRSg,3240
fairseq/examples/backtranslation/prepare-wmt18en2de.sh,sha256=B5K8SL5J3mpe864mXobfjGmRheNsUcGvSHhmOfc6Ks0,3699
fairseq/examples/backtranslation/sacrebleu.sh,sha256=ld6KjIcJ5yEk1NAk68sNDYkWEap6YJ54mNxs2JYspFE,961
fairseq/examples/backtranslation/tokenized_bleu.sh,sha256=YOxyOREpt0gwA2Pa1LulEvoRorad0lmmrwtKUL3r12g,1137
fairseq/examples/bart/README.glue.md,sha256=cyI6KXChitjOHQz1s6OTr_Iftw0XpJOEaVRXnfwvmhE,4060
fairseq/examples/bart/README.md,sha256=IHfgqSgPv7vA0O5g2j0dUPURpTKJmaNVpp8rnn2EKDg,9228
fairseq/examples/bart/README.summarization.md,sha256=ijyJmX8WspiBkHXGcyPBZ_-B8GbQXg65o8It59u-75A,3728
fairseq/examples/bart/__pycache__/summarize.cpython-311.pyc,,
fairseq/examples/bart/summarize.py,sha256=OaG_N0-hovD9kWLFNbNFvbGS4c_zG_Jfdv2B9COKI1U,3174
fairseq/examples/byte_level_bpe/README.md,sha256=wwjLQPufMeLLyEYX19O5Dk4NC4akNzRgXGdLbD7G2cY,3416
fairseq/examples/byte_level_bpe/__pycache__/get_bitext.cpython-311.pyc,,
fairseq/examples/byte_level_bpe/__pycache__/gru_transformer.cpython-311.pyc,,
fairseq/examples/byte_level_bpe/get_bitext.py,sha256=RwDPDoHO-r-Djomq17By1r2BAFpCivv9mZnfqurnejk,7993
fairseq/examples/byte_level_bpe/get_data.sh,sha256=AtzsS-7LinSvzHxwbwUMGmdR-AMEnLYAqqxsNFm3sYU,1890
fairseq/examples/byte_level_bpe/gru_transformer.py,sha256=JXDHNW0IzxLp0SKlUMVN0VBcCOVuGCrq3nTiVvcKs_s,5027
fairseq/examples/camembert/README.md,sha256=mPrO8crzARTzJPoGuhH_jacfUShtjgx8afB9S6p7HrQ,3982
fairseq/examples/constrained_decoding/README.md,sha256=8PKY0CBOABNy8k5qAOvvquv-dk-uP3ozCfEE-DgKC1U,5538
fairseq/examples/constrained_decoding/__pycache__/normalize.cpython-311.pyc,,
fairseq/examples/constrained_decoding/__pycache__/tok.cpython-311.pyc,,
fairseq/examples/constrained_decoding/normalize.py,sha256=GmQ6jaLRteKEtlS6cnD15Xfq54clrixjF3byAyZRdhI,698
fairseq/examples/constrained_decoding/tok.py,sha256=6Z-KumJSueJzT7ADJiChhfS51cat82FrK3qQfQVSqTM,844
fairseq/examples/conv_seq2seq/README.md,sha256=UoBX7ZHc2l-__u0M9xS7IekrkWWv5e0Hn01zYSW-LzA,1926
fairseq/examples/criss/README.md,sha256=XkE71cRA-6A6Y1eAYmnZN_0kbyV5GaDjPbswE-NWZ4Q,1731
fairseq/examples/criss/__pycache__/save_encoder.cpython-311.pyc,,
fairseq/examples/criss/download_and_preprocess_flores_test.sh,sha256=ptL5FuChflcnFnOj7Hr3PCermL68D1MBSC0p0pXGtGw,1740
fairseq/examples/criss/download_and_preprocess_tatoeba.sh,sha256=mqpQolOg_PglCuhY_-F-DveOpEZFtcI4UrlFNe9__Ac,1689
fairseq/examples/criss/mining/__pycache__/mine.cpython-311.pyc,,
fairseq/examples/criss/mining/mine.py,sha256=zXaHUOY2tPrz2JQg9Dntq-xguUuG2d8VauvVi814S4Q,8138
fairseq/examples/criss/mining/mine_example.sh,sha256=xYPBUO0ceuSDGtlsxnk1TMeLcfOBqV5i9b5tB53KPzs,3315
fairseq/examples/criss/save_encoder.py,sha256=MTHKIr_5SCTB2kSNnWtTp9T-53R1jFC7Us4Pb0mcP6Y,7473
fairseq/examples/criss/sentence_retrieval/__pycache__/encoder_analysis.cpython-311.pyc,,
fairseq/examples/criss/sentence_retrieval/encoder_analysis.py,sha256=5VsC-lS-3XnMi11Z3Q8l3_8Rcs1lb84RVbw-30L8P_k,3278
fairseq/examples/criss/sentence_retrieval/sentence_retrieval_tatoeba.sh,sha256=DImxpOO-tquUaQPRzFLdqdOQ6hu1jW--RaZoK4DBF2U,1816
fairseq/examples/criss/unsupervised_mt/eval.sh,sha256=QprObFLNPMxNS9JuSb2RF2sKotuodkcOGZjkDAXq68w,3961
fairseq/examples/cross_lingual_language_model/README.md,sha256=SEclRkdhHynsa3B3AoviAgxfOiSQHlEHfez5sWYlJPU,3051
fairseq/examples/data2vec/README.md,sha256=Jw97sDCm632DHIFKqbsCertOspJGIWulbJ9kt0RMG2U,7289
fairseq/examples/data2vec/config/audio/pretraining/base_librispeech.yaml,sha256=eQliajoXxpdRXsBDFYbdyOD0tlDXfm-eN3-57ZG3U8E,1411
fairseq/examples/data2vec/config/text/pretraining/base.yaml,sha256=p2Pgp5flbeGh9FsRPheRju9vYacyUWPcg0lpc957F2c,1457
fairseq/examples/data2vec/models/__pycache__/data2vec_audio.cpython-311.pyc,,
fairseq/examples/data2vec/models/__pycache__/data2vec_text.cpython-311.pyc,,
fairseq/examples/data2vec/models/data2vec_audio.py,sha256=6i__ZKfXeIjiweviKfnQQNwimTXfpo2vqXn5-JY7sX0,17916
fairseq/examples/data2vec/models/data2vec_text.py,sha256=jI-qxgZ-CXVtvWeGtnQuIvsmFdTX2o7mcvFJbj_4CYA,18697
fairseq/examples/discriminative_reranking_nmt/README.md,sha256=wxnHJ2Dzhu8J_t4R3ga2Fbk6PTRUNZAiGV0nWOIMcTo,7095
fairseq/examples/discriminative_reranking_nmt/__init__.py,sha256=2O1C-8qhsUZ76e9vMLSk9MHWX3bj_b9XkZRdRp_vtto,48
fairseq/examples/discriminative_reranking_nmt/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/discriminative_reranking_nmt/__pycache__/drnmt_rerank.cpython-311.pyc,,
fairseq/examples/discriminative_reranking_nmt/config/deen.yaml,sha256=x8K6MGzmFL2IIfZGP-y3gR3ieiwFGkJN9kddErLnAQc,954
fairseq/examples/discriminative_reranking_nmt/criterions/__init__.py,sha256=g9jr4CEhVb03y0ie1OrI9Mm95GCo4pKVjJpqZQCjWlE,133
fairseq/examples/discriminative_reranking_nmt/criterions/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/discriminative_reranking_nmt/criterions/__pycache__/discriminative_reranking_criterion.cpython-311.pyc,,
fairseq/examples/discriminative_reranking_nmt/criterions/discriminative_reranking_criterion.py,sha256=KVDhRzEsf6KfuNBaKLlAiXFzpkUy2Br-_-2pTTZhNAY,4997
fairseq/examples/discriminative_reranking_nmt/drnmt_rerank.py,sha256=ciKJ0GjB0h05HX-rYEwtWCj4VAPo6vitYaGPwABsGSw,11312
fairseq/examples/discriminative_reranking_nmt/models/__init__.py,sha256=D_HxIMgRPm-XUOlpNYY7RuGaCJGO0sgIGis7CgN6u1w,119
fairseq/examples/discriminative_reranking_nmt/models/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/discriminative_reranking_nmt/models/__pycache__/discriminative_reranking_model.cpython-311.pyc,,
fairseq/examples/discriminative_reranking_nmt/models/discriminative_reranking_model.py,sha256=JkBtZbi-5QhxH3yu3kYsHbQsDJL7Pwp8OV_QfZM4ulw,13714
fairseq/examples/discriminative_reranking_nmt/scripts/__pycache__/prep_data.cpython-311.pyc,,
fairseq/examples/discriminative_reranking_nmt/scripts/prep_data.py,sha256=rGrTRFgPqul46q1sBrmMPFvshp-L1t3m2MOnpXeWLBk,4872
fairseq/examples/discriminative_reranking_nmt/tasks/__init__.py,sha256=R-mMoCm3Cl7h9H7IVCpZPR4ME8StrhqQg57XQ7rbFk8,128
fairseq/examples/discriminative_reranking_nmt/tasks/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/discriminative_reranking_nmt/tasks/__pycache__/discriminative_reranking_task.cpython-311.pyc,,
fairseq/examples/discriminative_reranking_nmt/tasks/discriminative_reranking_task.py,sha256=WXuH9rjDLh4rXsr3RURCmv1fxk6ab4ifIaA1ecxG8FE,17731
fairseq/examples/fast_noisy_channel/README.md,sha256=HCZ4wPyT0Y3USIMQ6hwL5Yy-IlRui2LOM1Hl2mxZ3xw,20651
fairseq/examples/fast_noisy_channel/__init__.py,sha256=vB9bYmDoDnjOefX8jEek8l1kZ_FpQdpQtJi35PiWx_Q,329
fairseq/examples/fast_noisy_channel/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/fast_noisy_channel/__pycache__/noisy_channel_beam_search.cpython-311.pyc,,
fairseq/examples/fast_noisy_channel/__pycache__/noisy_channel_sequence_generator.cpython-311.pyc,,
fairseq/examples/fast_noisy_channel/__pycache__/noisy_channel_translation.cpython-311.pyc,,
fairseq/examples/fast_noisy_channel/noisy_channel_beam_search.py,sha256=_G4fCXsdnUH86GZUlu9MRf7FEZi1dCjtcQM2M85vdqs,2895
fairseq/examples/fast_noisy_channel/noisy_channel_sequence_generator.py,sha256=ZwBH7EcH4NloVqCMb6oN6Nw3tgSWi6pPEkT4rRGeJiE,41200
fairseq/examples/fast_noisy_channel/noisy_channel_translation.py,sha256=N9Jibb4LOOq1LpKGObFOAj_Jv79-J6sfr0bCyBfpWEk,6709
fairseq/examples/flores101/README.md,sha256=7mQVPuo_lCY7cdwi8JMbtSgA1b4R5qYfDZbmKZwNORs,4604
fairseq/examples/flores101/flores_logo.png,sha256=QwxO7v6JsVJ9dKuIwEJHzX4hwcYqqgWUdiXvnOiqZ-0,33184
fairseq/examples/fully_sharded_data_parallel/README.md,sha256=Z7ObJLdQp90_tVPoBeaMfujAb1tjElVUYr3IudEufPg,15626
fairseq/examples/gottbert/README.md,sha256=6kt-vq5W27OWwQjP1U49ngRrtKaXBGxVRtqbY8wZr98,2092
fairseq/examples/hubert/README.md,sha256=GLoBQ4pjHZk7mumSvjXYVPjxIzf-nlBE-CkuNuKyZh4,5079
fairseq/examples/hubert/__pycache__/measure_teacher_quality.cpython-311.pyc,,
fairseq/examples/hubert/__pycache__/update_ckpt.cpython-311.pyc,,
fairseq/examples/hubert/config/decode/ax_sweep/ngram.yaml,sha256=ELRgLCcm0vDcABuGkZ6IVyFhLy88dCXdX6mQNxV28wM,837
fairseq/examples/hubert/config/decode/ax_sweep/transformer.yaml,sha256=fO1_BB5sn6V1I7dCaHkY46e1iCvxJk42DLmmqec5zro,836
fairseq/examples/hubert/config/decode/infer_fsqlm.yaml,sha256=Ib10-CNFkvNTxdPw2RX8VQYXlVt2mJnygXFXrRjz3P4,784
fairseq/examples/hubert/config/decode/infer_kenlm.yaml,sha256=-Si1F68cHUa3LFkzq7lPyV7YGiIYW9wWURnp3mpPrnc,781
fairseq/examples/hubert/config/decode/infer_viterbi.yaml,sha256=Z1Jhqeh8WMl7f8oidViitIsaSdWeA2vghOG7Xc1SKX0,446
fairseq/examples/hubert/config/decode/run/submitit_slurm.yaml,sha256=Ogvo_9kA2YDpiqUjq-gYa3EbyLaOvqpxxLlu33x7gxg,488
fairseq/examples/hubert/config/decode/run/submitit_slurm_8gpu.yaml,sha256=XZxDf6NMV50J2OK556BO_tx4XkRJ30Jbi3fdC8_SfOo,488
fairseq/examples/hubert/config/finetune/base_10h.yaml,sha256=2hcx9E0QQ6dyeRZCAlyeNVja__svKDUaYnLcyta7XEQ,1910
fairseq/examples/hubert/config/finetune/ckpt/it1.yaml,sha256=MMPwGuzEhKihmkZwwxQQXDzvrwizTLotN3FnsowXdLo,236
fairseq/examples/hubert/config/finetune/lm/ls_4gram.yaml,sha256=jrS0fVwEY7_pwqvkRNNiMNNdruiJvlsNQAQVHz1Xi60,255
fairseq/examples/hubert/config/finetune/run/submitit_reg.yaml,sha256=VT2QYbKCdYXEvl8cnj-AtRD-vKjAzWp1bbZYgCNKxB8,469
fairseq/examples/hubert/config/pretrain/data/iter1.yaml,sha256=6kZIVc2-XPIpM36BVet8mE74Ndtl8CmymRRC3zp9qeE,87
fairseq/examples/hubert/config/pretrain/data/iter2.yaml,sha256=MUlCfCc1_ETwsaDtrg7jpv8XhKAmAsKDhu97XUk_sbE,86
fairseq/examples/hubert/config/pretrain/hubert_base_librispeech.yaml,sha256=Rj-Xg85Ue2IdJuNfyB4t0bACRHQAJ4O9K8pJ6Pm5uYA,1857
fairseq/examples/hubert/config/pretrain/hubert_large_librivox.yaml,sha256=8IkTVlCwELYtd6V1MHTLH0NyP1plbeCBy62w3tSASEw,2042
fairseq/examples/hubert/config/pretrain/hubert_xlarge_librivox.yaml,sha256=bjgFM6uVjoSeoqi6Op4K0R5zAFqhuP_n1tFnnlTnMdM,2042
fairseq/examples/hubert/config/pretrain/run/submitit_reg.yaml,sha256=L5jEVImj-NAcvAsv-gxBVcE0TgGV6POvdjTFdr9DjfA,470
fairseq/examples/hubert/measure_teacher_quality.py,sha256=Jzm-66TbXPrcF_8ybnsZgXRSZKlZyMsJtr6PtEwCD5I,6826
fairseq/examples/hubert/simple_kmeans/README.md,sha256=vXLh3x11izty2m-ggxotBeBrTQyzMzQlSC1N2MWa6gQ,2312
fairseq/examples/hubert/simple_kmeans/__pycache__/dump_hubert_feature.cpython-311.pyc,,
fairseq/examples/hubert/simple_kmeans/__pycache__/dump_hubert_feature_s2t.cpython-311.pyc,,
fairseq/examples/hubert/simple_kmeans/__pycache__/dump_km_label.cpython-311.pyc,,
fairseq/examples/hubert/simple_kmeans/__pycache__/dump_mfcc_feature.cpython-311.pyc,,
fairseq/examples/hubert/simple_kmeans/__pycache__/dump_w2v2_feature.cpython-311.pyc,,
fairseq/examples/hubert/simple_kmeans/__pycache__/feature_utils.cpython-311.pyc,,
fairseq/examples/hubert/simple_kmeans/__pycache__/learn_kmeans.cpython-311.pyc,,
fairseq/examples/hubert/simple_kmeans/dump_hubert_feature.py,sha256=ZSJTAlfhM_-96dQ0zcPh8LkcjthNwifSb2yJ4S9nCK0,3120
fairseq/examples/hubert/simple_kmeans/dump_hubert_feature_s2t.py,sha256=mdhdhWXh8Dh07rrKL3BiQzfVB_gbor3L3sVdGY8gMd8,2775
fairseq/examples/hubert/simple_kmeans/dump_km_label.py,sha256=zMKSY-XClCCcfVMJfx7Y7q38KxpTmwBShBp5-FGy1MM,3008
fairseq/examples/hubert/simple_kmeans/dump_mfcc_feature.py,sha256=-YkPwpbXx0pKr3f0tvG5HY8KldRLsWmTaruhvJutVtY,2495
fairseq/examples/hubert/simple_kmeans/dump_w2v2_feature.py,sha256=j-gmqj2WbeFpLK03tYkqiDKApVz_FNxTNKIqHP-rLm8,3129
fairseq/examples/hubert/simple_kmeans/feature_utils.py,sha256=fwS1pbEDFzheDzbgr1mvN6RJuIwCm0MXHlErW6x9fYs,2008
fairseq/examples/hubert/simple_kmeans/learn_kmeans.py,sha256=EncugUqG7TXumFfwfH61VHUFuqUzSTi6mP3x-45yxEg,4000
fairseq/examples/hubert/tests/6313-76958-0021.flac,sha256=DdieK_jGDgUmS_JTnw7DW8Ze-1XNlSs1EPokPenMFv8,223912
fairseq/examples/hubert/tests/sample.base.L9.km500.km,sha256=QAVSwXE6cfMbmooeLmnpFrJlP-ms6cQUjmD4mlDiis4,2245
fairseq/examples/hubert/tests/sample.base.L9.len,sha256=Xoqkqsm8GdSR4KVLkrOU4bqY-eLBz6tSpC3_mC7_30s,4
fairseq/examples/hubert/tests/sample.base.L9.npy,sha256=tE3DoFGfattnCl2nQQwUBfT9sOSGbl_EmDsTZIAhLzQ,1831104
fairseq/examples/hubert/tests/sample.large.L20.len,sha256=Xoqkqsm8GdSR4KVLkrOU4bqY-eLBz6tSpC3_mC7_30s,4
fairseq/examples/hubert/tests/sample.large.L20.npy,sha256=TIKDkTTMI0A1XrSbQefjUX6Onf36pvwo8EZMyK6Vae4,2441408
fairseq/examples/hubert/tests/sample.large.hypo.word,sha256=w4Ejw9ZpfBEWMsDdLZFM83azpiLfLWShdbV0X0JRZxA,226
fairseq/examples/hubert/tests/sample.xlarge.L30.len,sha256=Xoqkqsm8GdSR4KVLkrOU4bqY-eLBz6tSpC3_mC7_30s,4
fairseq/examples/hubert/tests/sample.xlarge.L30.npy,sha256=u-nwkp7NTFh4a-VSFdg_hXh_8NgRlrwuc0FPgqiTmAY,3051712
fairseq/examples/hubert/tests/sample.xlarge.hypo.word,sha256=aol3WgRURbVa2CE-9qQvXF9wpeMHU7hVyya39oW7HHc,226
fairseq/examples/hubert/tests/test_feature_and_unit.sh,sha256=uTQ4Ng1fgj8k0mJIyf148pypJg7f8007NM-0LCy7tbs,2331
fairseq/examples/hubert/tests/test_finetuned_asr.sh,sha256=guoD3MPnNIweYJocTkG_esNtgZsOZ4IcFnmjida_484,1757
fairseq/examples/hubert/update_ckpt.py,sha256=EyECl7ltXjPeG-5x0BVRsCWmAP82pKQAvEmxR0w7Ni0,873
fairseq/examples/joint_alignment_translation/README.md,sha256=Ilkwttm0_UMdLg9lIrEGQa30reDOLFK749pIZyCHDVY,3130
fairseq/examples/joint_alignment_translation/prepare-wmt18en2de_no_norm_no_escape_no_agressive.sh,sha256=0-BCeMHj2Okf-ZziJqw_ol2yb3C2CTXyDp1B1eeKlsk,3339
fairseq/examples/language_model/README.adaptive_inputs.md,sha256=72i_opMSz3_FQ-hZPZpS-KI6j2cd5RtDKfLFWO60bng,2000
fairseq/examples/language_model/README.conv.md,sha256=GkNe6lXFXe5CJgVZM3wf5x-sPnoBNFt7lh5znJ9rXmE,1265
fairseq/examples/language_model/README.md,sha256=4S_iyMEOvg6GZY3x4Yvak07s1MVQIIIDAs3OkJ0Xdzo,5465
fairseq/examples/language_model/prepare-wikitext-103.sh,sha256=pK4f8lA7L6mOBQGgXFbRFvKlzdDc9V2sIa_kSLPOX5A,827
fairseq/examples/laser/README.md,sha256=5YGv61mijmkMe24d_2MZYxPs2Z3jEtKRkBswub5KpWI,5305
fairseq/examples/laser/laser_src/__init__.py,sha256=Gztp67baV0KIzLT2IS6QE6zhEp-iJosRdyAATyX_t_E,287
fairseq/examples/laser/laser_src/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/laser/laser_src/__pycache__/laser_lstm.cpython-311.pyc,,
fairseq/examples/laser/laser_src/__pycache__/laser_task.cpython-311.pyc,,
fairseq/examples/laser/laser_src/__pycache__/laser_transformer.cpython-311.pyc,,
fairseq/examples/laser/laser_src/__pycache__/multitask_data_utils.cpython-311.pyc,,
fairseq/examples/laser/laser_src/laser_lstm.py,sha256=e3vjm7BZP9DYVCy-8LgIpDq8cSVzp5nW6PCVBwZgjl0,20672
fairseq/examples/laser/laser_src/laser_task.py,sha256=D1rJhBTtwMg-cT-YB_-MpE3V7IBSJ8Entm0MFZh2Ads,11108
fairseq/examples/laser/laser_src/laser_transformer.py,sha256=E9iBIuM39lDMcWMH_2bb5oVBz-vp4IiVZZdDtkcbMBs,11947
fairseq/examples/laser/laser_src/multitask_data_utils.py,sha256=3GsMQERfHKsHwx9PEfYn3zsy5XeHOwOkzoDu32FN994,4332
fairseq/examples/latent_depth/README.md,sha256=RIsSvN7_WeBrqKs1uSvSAAhreCo-2hr6vMg1xfcpFKQ,2972
fairseq/examples/latent_depth/latent_depth_src/__init__.py,sha256=CfBQjUk9K2un-FWS7Myp1tzRD6zCa5DWKZJQ-rI-EqQ,380
fairseq/examples/latent_depth/latent_depth_src/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/latent_depth/latent_depth_src/__pycache__/multilingual_translation_latent_depth.cpython-311.pyc,,
fairseq/examples/latent_depth/latent_depth_src/loss/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
fairseq/examples/latent_depth/latent_depth_src/loss/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/latent_depth/latent_depth_src/loss/__pycache__/latent_depth.cpython-311.pyc,,
fairseq/examples/latent_depth/latent_depth_src/loss/latent_depth.py,sha256=_Yvp1g8CGOIZvAGl4cZGVnlZW7C9PRnH8zihlnvvHsA,3802
fairseq/examples/latent_depth/latent_depth_src/models/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
fairseq/examples/latent_depth/latent_depth_src/models/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/latent_depth/latent_depth_src/models/__pycache__/latent_multilingual_transformer.cpython-311.pyc,,
fairseq/examples/latent_depth/latent_depth_src/models/__pycache__/latent_transformer.cpython-311.pyc,,
fairseq/examples/latent_depth/latent_depth_src/models/latent_multilingual_transformer.py,sha256=xK8RjHy-JaXLcp1mdagdZjD_R9khDaW1MCseKTzEJK8,3211
fairseq/examples/latent_depth/latent_depth_src/models/latent_transformer.py,sha256=Roc1H65IkZ3ZGVJV68c4SADAz2VZ73CBevyH60y_pPg,5584
fairseq/examples/latent_depth/latent_depth_src/modules/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
fairseq/examples/latent_depth/latent_depth_src/modules/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/latent_depth/latent_depth_src/modules/__pycache__/latent_layers.cpython-311.pyc,,
fairseq/examples/latent_depth/latent_depth_src/modules/latent_layers.py,sha256=G0LieEqD3hB5HHLslyfyyxmybZjKPURfrB-dN5YgNjs,2605
fairseq/examples/latent_depth/latent_depth_src/multilingual_translation_latent_depth.py,sha256=LRW1wEAjLd_G3wTxccvY5zTJE8l6w1qCTxFgzaKgrNM,8592
fairseq/examples/layerdrop/README.md,sha256=QW000113to5CMU88UGqq2GJMlvaG72NJRw_ZyQpr2kM,8432
fairseq/examples/linformer/README.md,sha256=v5ZKXD-W4xHniFt17P9R-dOEuowCqK-ZEBZPxd6BC7I,789
fairseq/examples/linformer/linformer_src/__init__.py,sha256=TQcLhOt-SnuZmctR3H2An4iVVlg-x8ELPNoRFFyU3d0,224
fairseq/examples/linformer/linformer_src/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/linformer/linformer_src/models/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
fairseq/examples/linformer/linformer_src/models/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/linformer/linformer_src/models/__pycache__/linformer_roberta.cpython-311.pyc,,
fairseq/examples/linformer/linformer_src/models/linformer_roberta.py,sha256=YDwm_D2GE8TsThBqY24gYs7CgQI3AndxZ5nNErXWvzM,4143
fairseq/examples/linformer/linformer_src/modules/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
fairseq/examples/linformer/linformer_src/modules/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/linformer/linformer_src/modules/__pycache__/linformer_sentence_encoder.cpython-311.pyc,,
fairseq/examples/linformer/linformer_src/modules/__pycache__/linformer_sentence_encoder_layer.cpython-311.pyc,,
fairseq/examples/linformer/linformer_src/modules/__pycache__/multihead_linear_attention.cpython-311.pyc,,
fairseq/examples/linformer/linformer_src/modules/linformer_sentence_encoder.py,sha256=aIPg7eFtEQXgAdQLhOOjoof2GCSlr6SbdZ0ufRgNUtM,2151
fairseq/examples/linformer/linformer_src/modules/linformer_sentence_encoder_layer.py,sha256=6wflLxFC8XC9KFghkK9SCimIW0-8C8tZXnbk8Cx3nzQ,2743
fairseq/examples/linformer/linformer_src/modules/multihead_linear_attention.py,sha256=Q9LrSXB8Oh9CB3bv8CqOObs3H6G_v1p_Z7jMfOEie9A,19151
fairseq/examples/m2m_100/README.md,sha256=ldi7S-veVKpeTIjGvaSKa6hwEncS2a_3LtJYy-Ph7j0,10860
fairseq/examples/m2m_100/install_dependecies.sh,sha256=tHw3mP5WbcqQ15qO-7Vhq9Pw8YuBpQO2gejSGngZmYc,2380
fairseq/examples/m2m_100/process_data/__pycache__/clean_histogram.cpython-311.pyc,,
fairseq/examples/m2m_100/process_data/__pycache__/dedup_data.cpython-311.pyc,,
fairseq/examples/m2m_100/process_data/__pycache__/remove_too_much_punc.cpython-311.pyc,,
fairseq/examples/m2m_100/process_data/clean_histogram.py,sha256=JOvWVVkW28-oLqGZgudWUFDY9a_Gtx53XHr66Ju655o,2010
fairseq/examples/m2m_100/process_data/dedup_data.py,sha256=c7W27BGoMzyLgbt2urLkni1fnUt50nZvyzJS9X2BV08,3132
fairseq/examples/m2m_100/process_data/remove_too_much_punc.py,sha256=vhdwQnuK51PXQ-Yrrh57Oer25CEX097_XhHxv1L_3M4,1545
fairseq/examples/m2m_100/tok.sh,sha256=l3JjN1ZujANlhIOASvJEe9s9cu7e3BwB8z2lKtgt18M,2391
fairseq/examples/m2m_100/tokenizers/README.md,sha256=dMerC754DOiadtFlHB2R9Kif_JZSaT0hdON6PEgMZ0o,733
fairseq/examples/m2m_100/tokenizers/__pycache__/tokenize_indic.cpython-311.pyc,,
fairseq/examples/m2m_100/tokenizers/__pycache__/tokenize_thai.cpython-311.pyc,,
fairseq/examples/m2m_100/tokenizers/__pycache__/tokenize_zh.cpython-311.pyc,,
fairseq/examples/m2m_100/tokenizers/seg_ja.sh,sha256=Vh_2p9K_asr4Jpq7GLBGY0GW5H9h-SB7MZhcqgQYe9k,399
fairseq/examples/m2m_100/tokenizers/seg_ko.sh,sha256=X2NRAVmn1Z5m7c5KQhcnbWwzTYvG9a7CiZfWMxolfzw,397
fairseq/examples/m2m_100/tokenizers/thirdparty/.gitignore,sha256=rl_r1SAq7Gn9xBzwhyAgxOupM6AsdUHJU48LYOq1p58,232
fairseq/examples/m2m_100/tokenizers/tokenize_indic.py,sha256=QLpB3VamXMCkhOzmkhbH0JahJaq65oW0KuO6zMMYZc4,727
fairseq/examples/m2m_100/tokenizers/tokenize_thai.py,sha256=tZN_WaPgm66K_lCP7kKDa0Pv0yDjWqSrNWV5u3vLVyE,323
fairseq/examples/m2m_100/tokenizers/tokenize_zh.py,sha256=b8xYF0Pb4l2BYedoaSnFWT_5XECfNeSBRXTMZt3zb-Q,309
fairseq/examples/m2m_100/tokenizers/tokenizer_ar.sh,sha256=cG4cLOk4Vx32ec8tm_3l2-HaVhv8nqedgKePBsAKxi0,758
fairseq/examples/mbart/README.md,sha256=wUXn75kDSo-qWXGqUsigkjL5kkLMBxxXWycnT2oVKPg,4785
fairseq/examples/megatron_11b/README.md,sha256=r2myPDnDLIfGNGW-E1eulquGwbjvkUc5Aj9Q39mtdCw,5247
fairseq/examples/megatron_11b/__pycache__/detok.cpython-311.pyc,,
fairseq/examples/megatron_11b/detok.py,sha256=TDwqTaoBFY9Qk0Dju2riT2ibzR93FKLBEyh4176-0bg,825
fairseq/examples/moe_lm/README.md,sha256=VbskB2xenIVoFjsaxQ3eGP3usx4kjwjzFmqdZ3fjwWE,5870
fairseq/examples/moe_lm/data_card.md,sha256=9Br0OPMh2wTj4u0N2pqf-QSdMlvdo3yNPiYa1VeKxfQ,16858
fairseq/examples/moe_lm/model_card.md,sha256=GnQrsMddw66yvEvkfCvlJl6p_LMXSbGvsT7R14oNaqg,11001
fairseq/examples/multilingual/ML50_langs.txt,sha256=YyhJSMtmEes1WvQhIaAco6CbfkJDQqKDdsIeP8NcNKU,311
fairseq/examples/multilingual/README.md,sha256=zrw5izqFNObp-psJQuS6khxUz4mrK5W3Kbsv6TzUEnk,7753
fairseq/examples/multilingual/data_scripts/README.md,sha256=BY9uLMGgaj-CLjgY42YaGGOS7j_ym15GOCJoi9Tbm8w,626
fairseq/examples/multilingual/data_scripts/__pycache__/binarize.cpython-311.pyc,,
fairseq/examples/multilingual/data_scripts/__pycache__/check_iswlt_test_data.cpython-311.pyc,,
fairseq/examples/multilingual/data_scripts/__pycache__/check_self_overlaps.cpython-311.pyc,,
fairseq/examples/multilingual/data_scripts/__pycache__/check_valid_test_overlaps.cpython-311.pyc,,
fairseq/examples/multilingual/data_scripts/__pycache__/dedup_all.cpython-311.pyc,,
fairseq/examples/multilingual/data_scripts/__pycache__/download_ted_and_extract.cpython-311.pyc,,
fairseq/examples/multilingual/data_scripts/__pycache__/download_wmt19_and_before.cpython-311.pyc,,
fairseq/examples/multilingual/data_scripts/__pycache__/remove_valid_test_in_train.cpython-311.pyc,,
fairseq/examples/multilingual/data_scripts/binarize.py,sha256=hH3rai59w1t7QUIUeqQxWu4pjB7aS6EMUswCTGoLLzw,6944
fairseq/examples/multilingual/data_scripts/check_iswlt_test_data.py,sha256=1LQK_rnLVy-h9gORI7aax0lXEhydJOAflZRWfje2kXU,2761
fairseq/examples/multilingual/data_scripts/check_self_overlaps.py,sha256=VJ7IRudjD1D4I7R6Wc7f_vwP-4u_5pEtTsOaPBRNGmY,3695
fairseq/examples/multilingual/data_scripts/check_valid_test_overlaps.py,sha256=M7IOYMQt7av3s1dmbPeIcOqNZoze_EjoU-enlZ19GuM,4515
fairseq/examples/multilingual/data_scripts/dedup_all.py,sha256=2_Ryge5_cNvBV4lk7iUrOy3aTpdUcFW0TnD_qhPKeRk,1796
fairseq/examples/multilingual/data_scripts/download_ML50_v1.sh,sha256=Vm251zt8sMe0CRr10XLuPmkZmRyFe7vaLzqRbK_hwOc,964
fairseq/examples/multilingual/data_scripts/download_af_xh.sh,sha256=v_r1MiyXzS5ujXzb5prUOJvfv5x_hM5YunA--F0q5P0,4370
fairseq/examples/multilingual/data_scripts/download_flores_data.sh,sha256=ZTa0XBDQVz9Q3NF3ufqHgkgFs8vW1qvsPgDZEWUGcjg,9713
fairseq/examples/multilingual/data_scripts/download_iitb.sh,sha256=Cb7zJY3bXr5yOCmHpqapdl1u6Bn9IUL8y5qEAXAniEc,1025
fairseq/examples/multilingual/data_scripts/download_iwslt_and_extract.sh,sha256=kCicFC88o4pg8ByqYCa56WjvOteGQk06te483GgeFwI,6210
fairseq/examples/multilingual/data_scripts/download_lotus.sh,sha256=qZZLR_sL8E1p4SIb_v-vN9n7bAJEL9JwdX0v9tZjEgw,1883
fairseq/examples/multilingual/data_scripts/download_ted_and_extract.py,sha256=RTJZwY4o99W0uSH5eCw4JKblDhqEdE1UrkEc-r3O91I,12515
fairseq/examples/multilingual/data_scripts/download_wat19_my.sh,sha256=PaNMqUOtBZcMQFveMl-BUjfEI9wU88VuTmv_reG04G0,1209
fairseq/examples/multilingual/data_scripts/download_wmt19_and_before.py,sha256=bCLOeSUb7mPCCwDffs7046hhT-2_ENp1NUXPy4g6O5k,37648
fairseq/examples/multilingual/data_scripts/download_wmt20.sh,sha256=02YqWaKxNjhIQwT7XJiUTi6XZCQTSi8UWTsWPXYrLmQ,23814
fairseq/examples/multilingual/data_scripts/preprocess_ML50_v1.sh,sha256=9XCNn7PkyfadBOOONthwJ_ZPOm-OXPDGjlcOpAF7NEc,846
fairseq/examples/multilingual/data_scripts/remove_valid_test_in_train.py,sha256=JPLpWN4l14Y22b44HihpMu1Ul2DC7jAUR1geUp9HqTI,11158
fairseq/examples/multilingual/data_scripts/requirement.txt,sha256=45flAyNiF28Ykc3FaNTL2_XH7NwVvheZLQmkb57khKU,11
fairseq/examples/multilingual/data_scripts/utils/__pycache__/dedup.cpython-311.pyc,,
fairseq/examples/multilingual/data_scripts/utils/__pycache__/fasttext_multi_filter.cpython-311.pyc,,
fairseq/examples/multilingual/data_scripts/utils/dedup.py,sha256=2ixtywrRwW7-I4Vv2Hog4Q2KhFMwcVHiIhJ0MOxaL5A,1459
fairseq/examples/multilingual/data_scripts/utils/fasttext_multi_filter.py,sha256=IIzb0FYdYpHikwvd2E4uRtLPIt7N23U9ZfBxo2RMQRM,2340
fairseq/examples/multilingual/data_scripts/utils/strip_sgm.sh,sha256=N_37jkxivmUCk0CVNWIUYLvxcsixcXtu44QnpmRRX2U,67
fairseq/examples/multilingual/finetune_multilingual_model.sh,sha256=wQz9zJikhutGw0Vuj8AQgjYJZEO8RCDwpM6-lx3n8x8,1454
fairseq/examples/multilingual/multilingual_fairseq_gen.sh,sha256=rikgfID-gPZPxfw1d489y7jH74LyDzUpgKvQns5wseI,790
fairseq/examples/multilingual/train_multilingual_model.sh,sha256=_Ug9IdoaPcUjVg8U_pFnxF98PPv1JZ_uq5oTXYexrkg,1301
fairseq/examples/noisychannel/README.md,sha256=zBZ7Fm_X_ayobNWAGNLPm3t1Su9v8Aw4eC3hkY7i_LM,3666
fairseq/examples/noisychannel/__init__.py,sha256=-YMYet22WhwfM1NHIIRcGfuOZw7jnavXFORV4p0Q5gY,216
fairseq/examples/noisychannel/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/noisychannel/__pycache__/rerank.cpython-311.pyc,,
fairseq/examples/noisychannel/__pycache__/rerank_generate.cpython-311.pyc,,
fairseq/examples/noisychannel/__pycache__/rerank_options.cpython-311.pyc,,
fairseq/examples/noisychannel/__pycache__/rerank_score_bw.cpython-311.pyc,,
fairseq/examples/noisychannel/__pycache__/rerank_score_lm.cpython-311.pyc,,
fairseq/examples/noisychannel/__pycache__/rerank_tune.cpython-311.pyc,,
fairseq/examples/noisychannel/__pycache__/rerank_utils.cpython-311.pyc,,
fairseq/examples/noisychannel/rerank.py,sha256=gq30PPpElod29FFxIVYbTt8R1ZBfvgHjYkCSarB6ArU,14097
fairseq/examples/noisychannel/rerank_generate.py,sha256=tzxbXQuYKNzok_-P53BLajOQg1flJepjBAlZG3qTk9I,14157
fairseq/examples/noisychannel/rerank_options.py,sha256=mL4GbQCgqqmtHyG0NqcaN3-1rNxNQEk9zRmimEMqOj0,7537
fairseq/examples/noisychannel/rerank_score_bw.py,sha256=Qua362XR9QW0UAwLFgi5CuEpFc3ocDV3TfDf2gYrZAU,4212
fairseq/examples/noisychannel/rerank_score_lm.py,sha256=SV5bCxbQpGHJhu3IluZZ1VukAkAVuurDyvv9j1ouxno,2253
fairseq/examples/noisychannel/rerank_tune.py,sha256=_J1JPqoxn8gQkxq_poqR8UW08_5Sm3_Vd5E1wo1bVlk,3166
fairseq/examples/noisychannel/rerank_utils.py,sha256=XqE8XK0Per-BaTj8AD8oGc-Inpa-9UB5tvcgx4uvdLU,28678
fairseq/examples/nonautoregressive_translation/README.md,sha256=Mfpcwu-h94vfHFPIfdrn9rhSjke9c9-MxBKy4a-dx4c,7448
fairseq/examples/nonautoregressive_translation/scripts.md,sha256=MlKlqKCF5jzwpnQjNLUbKBF5urykGjtQB7WF0zO4IDk,6039
fairseq/examples/normformer/README.md,sha256=PiQhDBgOKp2c-yI2n3fIQ5xVPumEWEucGdkPvalulfk,3270
fairseq/examples/normformer/train_lm.sh,sha256=KBPXSyMtrRAwlm7yM4FB7R2Oz38Qdzmk4heoF7lolHU,2036
fairseq/examples/operators/alignment_train_cpu.cpp,sha256=Vsx03UStIrjyVeGT7vZUh3HjgrcVtI78_oIo4TrxojI,4768
fairseq/examples/operators/alignment_train_cuda.cpp,sha256=Iu-geM93ZYe4mn0ExNpuy6B7lIPMuC6wCaLdlMyTLIc,649
fairseq/examples/operators/alignment_train_cuda.h,sha256=wTA7c5DQRJnlpVadlsxnzq-1Fp9BWsIC0tloatXHXF4,389
fairseq/examples/operators/alignment_train_kernel.cu,sha256=X_jGgs1KBL8c8axRyEQMAMDx3N6LeQ1HImlyv2SAHcg,10763
fairseq/examples/operators/utils.h,sha256=3c8d5vxAHtsdMy2vyS1W4XzkilaRysaoTSsnddI4rMo,528
fairseq/examples/paraphraser/README.md,sha256=yKHAaxaMDaQudUWWlIn_jGfN8FDvJJFSFFsh6KR-cTw,2761
fairseq/examples/paraphraser/__pycache__/paraphrase.cpython-311.pyc,,
fairseq/examples/paraphraser/paraphrase.py,sha256=8KPXW17bVnVbLqFhhFThekvGFtWBDaGJwb5CaugE22g,2408
fairseq/examples/pay_less_attention_paper/README.md,sha256=tk1i9PCyYlHxvlhcw-B8-dBipHzP_4fwHZGcbgPChEc,11056
fairseq/examples/pointer_generator/README.md,sha256=w678Q80ui8ZC9h2escwD-tostwHruDn6xVFCMe5mgcw,3919
fairseq/examples/pointer_generator/README.xsum.md,sha256=hW7mXvLc8Cuc9r51txDbJWMbzq3FKUconKVu1pm7tFk,6656
fairseq/examples/pointer_generator/__pycache__/postprocess.cpython-311.pyc,,
fairseq/examples/pointer_generator/__pycache__/preprocess.cpython-311.pyc,,
fairseq/examples/pointer_generator/pointer_generator_src/__init__.py,sha256=518e_JC1G-JFnO8gglW0zILVjzAxpwKJR6o08L4PFNA,215
fairseq/examples/pointer_generator/pointer_generator_src/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/pointer_generator/pointer_generator_src/__pycache__/transformer_pg.cpython-311.pyc,,
fairseq/examples/pointer_generator/pointer_generator_src/transformer_pg.py,sha256=uKtKoginWofw5kDzhMqkHWd8MBWBXZhLfWw3IPEc734,23439
fairseq/examples/pointer_generator/postprocess.py,sha256=UY6rk5jrMNWQR1YK2q9thogr8RKjBTISCNb3MQQNsJI,3266
fairseq/examples/pointer_generator/preprocess.py,sha256=6q_LkJB_r44agiPPAJvfiobehmf-5uvCxQkBQO2TqJs,3337
fairseq/examples/quant_noise/README.md,sha256=jIejd9Ujxek3S_PKBTkQluajTMtB6LtsfVyOnZHVuY8,15501
fairseq/examples/quant_noise/transformer_quantization_config.yaml,sha256=9yggy8L8TzpCilKAeUlrJMoRIJaGrgSM7pXHImzhAPc,1049
fairseq/examples/roberta/README.custom_classification.md,sha256=H6JzoZ4VfVQ411eeazAGHFc374vJqHLTRLNlwrW2cQE,5382
fairseq/examples/roberta/README.glue.md,sha256=FeemdZw-xPvNxJqO4kK7mAWOIkR7uGTTy-fY3P0Nkmw,2591
fairseq/examples/roberta/README.md,sha256=_343oLFJ1UpSEkJI4PghBTx7HXgiqd-C5WD_ApuWHVw,13105
fairseq/examples/roberta/README.pretraining.md,sha256=lqcPzFQmBwahWVzOVm1-HPFx8oomuUuBwsKy99SNkQ4,3434
fairseq/examples/roberta/README.race.md,sha256=Nkj21HJv7V97TJNfaFsFs5BIo762vdeADZtk_9Cogts,2758
fairseq/examples/roberta/__pycache__/multiprocessing_bpe_encoder.cpython-311.pyc,,
fairseq/examples/roberta/__pycache__/preprocess_RACE.cpython-311.pyc,,
fairseq/examples/roberta/commonsense_qa/README.md,sha256=yyiLjPzTGS_jQGbGXLpcBShCUMHV2WU2-vYY1Tvu6lg,4050
fairseq/examples/roberta/commonsense_qa/__init__.py,sha256=7UJC_H8PERV2JnNiJTwVUJkgkIjbRPAGCjUAx-dWKrc,220
fairseq/examples/roberta/commonsense_qa/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/roberta/commonsense_qa/__pycache__/commonsense_qa_task.cpython-311.pyc,,
fairseq/examples/roberta/commonsense_qa/commonsense_qa_task.py,sha256=JqlF2aZSiYkane6lYW68m3ydy3m8-aE4cQv8v0sjiJo,6147
fairseq/examples/roberta/commonsense_qa/download_cqa_data.sh,sha256=kHnKQaxu_JEkEOx7blbqtrA-JqhPVcyYJ6DvH5wB-ZI,594
fairseq/examples/roberta/config/finetuning/cola.yaml,sha256=shYmMYiNvaEdqPKk0L55SLWkgN27RG9uE_6Kz3YyKJo,983
fairseq/examples/roberta/config/finetuning/mnli.yaml,sha256=6JVDNp9o4ucBLOsygrnU4lJMiX4u2YxnVYvhSzVfgYc,986
fairseq/examples/roberta/config/finetuning/mrpc.yaml,sha256=lfsJhmBL3mdGRSsC18L6bMzGodQDCbfMK1MT5d069l0,983
fairseq/examples/roberta/config/finetuning/qnli.yaml,sha256=oBx2eGGTzwkhShEotblBOIP46Y7LChgjZ2nPJJIQ48A,985
fairseq/examples/roberta/config/finetuning/qqp.yaml,sha256=36SiyGcK_r1GH-kkS-UBnM4WkSn4bZMywCyr7tFYbho,987
fairseq/examples/roberta/config/finetuning/rte.yaml,sha256=HWjJY0al7TQ4jdJXo9_f1O77igQ4xNm18DV6_bvnXjA,983
fairseq/examples/roberta/config/finetuning/sst_2.yaml,sha256=_ipOOgQ5VeDMnwz-Yv69EjW6hiA-PI1fWIVrEi0C0-w,985
fairseq/examples/roberta/config/finetuning/sts_b.yaml,sha256=LlBASJfc7AJgCTmnxUwQje2iKLzPtTyQUsGkVUK2of4,934
fairseq/examples/roberta/config/pretraining/base.yaml,sha256=T9uW6YNp0Zezp3gweQwI_4he3JVtnL8A-ST3rqiesuA,626
fairseq/examples/roberta/multiprocessing_bpe_encoder.py,sha256=Uxx3Fa554qO_E0hWqZbc2ALyC4nV9wJNAuCCZ9sMAmk,3782
fairseq/examples/roberta/preprocess_GLUE_tasks.sh,sha256=z3qTdZpEJ1mQ66s9sIXOPuayuC5UzIFstbTRFPfUUuI,5738
fairseq/examples/roberta/preprocess_RACE.py,sha256=38vOqyIGJEd5vYC1NQG-BxOil55FaC14YXeRw7z4LY4,3429
fairseq/examples/roberta/preprocess_RACE.sh,sha256=M1V0SfYpwdflpgkg90f9EL4CzRUPNF4Buj2zdlixZms,2070
fairseq/examples/roberta/wsc/README.md,sha256=gmO_gToHF9zNUzBnpJ1HxPmqq74fag0QXRCuVUwuZLw,5649
fairseq/examples/roberta/wsc/__init__.py,sha256=RujLmd_4vqCc553rNUUg37Mh6MNinOXAuEt2nkgGetc,245
fairseq/examples/roberta/wsc/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/roberta/wsc/__pycache__/wsc_criterion.cpython-311.pyc,,
fairseq/examples/roberta/wsc/__pycache__/wsc_task.cpython-311.pyc,,
fairseq/examples/roberta/wsc/__pycache__/wsc_utils.cpython-311.pyc,,
fairseq/examples/roberta/wsc/wsc_criterion.py,sha256=0d5fQyDrdDbPbUvCNzMVLadEmdhx9CMMe0jin0N5CgQ,6037
fairseq/examples/roberta/wsc/wsc_task.py,sha256=NaVcS6PcYV_sG4442TwT24kSkohCjQJD6zKhcHbnFxI,13524
fairseq/examples/roberta/wsc/wsc_utils.py,sha256=BVk1WndmooBMHYEKciikr0T7TBj9p3tAaGPOiPUompI,8352
fairseq/examples/rxf/README.md,sha256=LDnbDHEtUDIXx-LBNAIQXvUnxelkooWCebQKorzINL4,2471
fairseq/examples/rxf/__init__.py,sha256=fUynFymd0ookYH3FZk5WnUzpk0xbmE4SIIWxwgboLrM,208
fairseq/examples/rxf/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/rxf/rxf_src/__init__.py,sha256=XttodfuJWbeHf-XrVKT7a6wuTqGVgOUXQVa8L-Vzcb0,258
fairseq/examples/rxf/rxf_src/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/rxf/rxf_src/__pycache__/label_smoothed_cross_entropy_r3f.cpython-311.pyc,,
fairseq/examples/rxf/rxf_src/__pycache__/sentence_prediction_r3f.cpython-311.pyc,,
fairseq/examples/rxf/rxf_src/label_smoothed_cross_entropy_r3f.py,sha256=dFz8w7omPOA1mKmak7svY_YflnpJbHNfliR5xoYLjHk,6109
fairseq/examples/rxf/rxf_src/sentence_prediction_r3f.py,sha256=C-jChY4u2rn9wAmwL5_R-64y0n_OknAZLQmlKuMBap0,6587
fairseq/examples/scaling_nmt/README.md,sha256=ja7DlHUNK3lsejP_3yVyLLMTUSMf6gU0fKBbD1GMITc,5225
fairseq/examples/shuffled_word_order/README.finetuning.md,sha256=uVuYgszU7NTzrCdAAswq93IM_c_s49xqhMKVSwxtwqI,5957
fairseq/examples/shuffled_word_order/README.md,sha256=OvgwxwOCylMkmD3i60qTVHEa44kejKN8WfK0K-O61es,9695
fairseq/examples/simultaneous_translation/README.md,sha256=F8BZRDJfkNerTqloOWG8EItGQEYme5-W9NhtdTzt1Q4,351
fairseq/examples/simultaneous_translation/__init__.py,sha256=JcpJIOTQypiU3zqnUKrs3VXSeKCnocGmMFK4VVXWVvA,207
fairseq/examples/simultaneous_translation/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/simultaneous_translation/docs/ende-mma.md,sha256=XYa-8J1ZW2COjIlauK-5wn6pIJS5JdzeG9XG9BnqlFw,2425
fairseq/examples/simultaneous_translation/docs/enja-waitk.md,sha256=ZfdprRf3ltshKmmEru44E6MvAd3DvI6L9TtkvLqsaX4,4331
fairseq/examples/simultaneous_translation/eval/agents/__pycache__/simul_t2t_enja.cpython-311.pyc,,
fairseq/examples/simultaneous_translation/eval/agents/simul_t2t_enja.py,sha256=tVF1skXUFVXV2ueHL7TYUrNdFnjTejrgfEBdNNwk6Og,7099
fairseq/examples/simultaneous_translation/models/__init__.py,sha256=InH3I_7_WK3yvD0JdeXWfN_NGz8IphsGHtJlkTNtgwk,482
fairseq/examples/simultaneous_translation/models/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/simultaneous_translation/models/__pycache__/convtransformer_simul_trans.cpython-311.pyc,,
fairseq/examples/simultaneous_translation/models/__pycache__/transformer_monotonic_attention.cpython-311.pyc,,
fairseq/examples/simultaneous_translation/models/convtransformer_simul_trans.py,sha256=aw8ad9A7r3vmlElkWBr7GmfSi84JlwPrSJNVH3nTU-s,7162
fairseq/examples/simultaneous_translation/models/transformer_monotonic_attention.py,sha256=umFKK5511BzXxODSVgGA2eiWQIHzBIlfEli7Z-hjgOE,10185
fairseq/examples/simultaneous_translation/modules/__init__.py,sha256=wKDGDmcdKypDPwbWF3ipZsh9lEkwWeZVAiK9zjryxWY,665
fairseq/examples/simultaneous_translation/modules/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/simultaneous_translation/modules/__pycache__/fixed_pre_decision.cpython-311.pyc,,
fairseq/examples/simultaneous_translation/modules/__pycache__/monotonic_multihead_attention.cpython-311.pyc,,
fairseq/examples/simultaneous_translation/modules/__pycache__/monotonic_transformer_layer.cpython-311.pyc,,
fairseq/examples/simultaneous_translation/modules/fixed_pre_decision.py,sha256=1mvEa_swhZbHnUj18e8oilrMWZPiVzLOmEPDzdAav1g,7370
fairseq/examples/simultaneous_translation/modules/monotonic_multihead_attention.py,sha256=-2W-wtd7wxJgxiHQK8-8jaT93lswmgI4kGhjaL1D8NA,16858
fairseq/examples/simultaneous_translation/modules/monotonic_transformer_layer.py,sha256=GLhuSvsi4hOWdzoGAJc1XcQs9mDmL7mKZDj2kMiBJvk,7265
fairseq/examples/simultaneous_translation/tests/__pycache__/test_alignment_train.cpython-311.pyc,,
fairseq/examples/simultaneous_translation/tests/__pycache__/test_text_models.cpython-311.pyc,,
fairseq/examples/simultaneous_translation/tests/test_alignment_train.py,sha256=BxqNncPWnZ-JsUk5zAHVx4CUliPICOZwRxe2REtekF8,2989
fairseq/examples/simultaneous_translation/tests/test_text_models.py,sha256=P3u-D85k1CcpX_pxNdrwMI68QYP4niRiIApqxD4ohPw,13524
fairseq/examples/simultaneous_translation/utils/__init__.py,sha256=5g7YOe-_YM0yu9mSy8gNnOIax5vof2s34gu1dIhJBiM,520
fairseq/examples/simultaneous_translation/utils/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/simultaneous_translation/utils/__pycache__/functions.cpython-311.pyc,,
fairseq/examples/simultaneous_translation/utils/__pycache__/monotonic_attention.cpython-311.pyc,,
fairseq/examples/simultaneous_translation/utils/__pycache__/p_choose_strategy.cpython-311.pyc,,
fairseq/examples/simultaneous_translation/utils/functions.py,sha256=P54YezfFO6_OdN9T1SFbMQCDKGWxbRttKPuCW-JAtIc,3539
fairseq/examples/simultaneous_translation/utils/monotonic_attention.py,sha256=OykKIN67bM5fpI64UUabMWUrz4B_RnDjr9_lDYXnqQ0,4987
fairseq/examples/simultaneous_translation/utils/p_choose_strategy.py,sha256=6-Uvud4i20pctNbKb1-yBW-f7mGzGzfKWcp_ftoy3nY,3445
fairseq/examples/speech_recognition/README.md,sha256=GJKI8DC6Ah_dQh0CO_-cWKg-q___kTeTAGy1sNUlxk4,5486
fairseq/examples/speech_recognition/__init__.py,sha256=2O1C-8qhsUZ76e9vMLSk9MHWX3bj_b9XkZRdRp_vtto,48
fairseq/examples/speech_recognition/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/speech_recognition/__pycache__/infer.cpython-311.pyc,,
fairseq/examples/speech_recognition/__pycache__/w2l_decoder.cpython-311.pyc,,
fairseq/examples/speech_recognition/criterions/ASG_loss.py,sha256=Tv7pw2g7zcW0IscNkqbk9F5RIRio1wTvOHuWCKf0CbI,5870
fairseq/examples/speech_recognition/criterions/__init__.py,sha256=sNWEGVUxyB-Y7t_2_ZSmWkxtOIuD66wkDo7-OMA6AAM,510
fairseq/examples/speech_recognition/criterions/__pycache__/ASG_loss.cpython-311.pyc,,
fairseq/examples/speech_recognition/criterions/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/speech_recognition/criterions/__pycache__/cross_entropy_acc.cpython-311.pyc,,
fairseq/examples/speech_recognition/criterions/cross_entropy_acc.py,sha256=qdmbv4NS-VXQBWKLOD_dcEveeo_NukVhcJ_v1tYDXH4,5372
fairseq/examples/speech_recognition/data/__init__.py,sha256=BQzqz_B1anjIwCYH4sFnOEsssy69B8q1GCGRDQTeEWs,248
fairseq/examples/speech_recognition/data/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/speech_recognition/data/__pycache__/asr_dataset.cpython-311.pyc,,
fairseq/examples/speech_recognition/data/__pycache__/collaters.cpython-311.pyc,,
fairseq/examples/speech_recognition/data/__pycache__/data_utils.cpython-311.pyc,,
fairseq/examples/speech_recognition/data/__pycache__/replabels.cpython-311.pyc,,
fairseq/examples/speech_recognition/data/asr_dataset.py,sha256=qLx2U8dt70KTcmhYp80iO-yRpVA1aydfS2u69tan7rc,3955
fairseq/examples/speech_recognition/data/collaters.py,sha256=cyIH_JpIK1hTQ02n0swa6-T3h0eVO4c44SICDqlPeXo,4796
fairseq/examples/speech_recognition/data/data_utils.py,sha256=3K4mHb2YAjt7ekp4tNJwVvWcL2M5rPOqchX2AiCjYqo,3429
fairseq/examples/speech_recognition/data/replabels.py,sha256=HBYsXlRayWCBTASxrIJ_hwNPUHZnzoLGTkN49coeik0,1970
fairseq/examples/speech_recognition/datasets/__pycache__/asr_prep_json.cpython-311.pyc,,
fairseq/examples/speech_recognition/datasets/asr_prep_json.py,sha256=5Qan0JI0lwIesAshkzLLJmhJyXrBnCg2aTyE_P8Hs-U,3775
fairseq/examples/speech_recognition/datasets/prepare-librispeech.sh,sha256=vweZoSwwhaDw_KI_9AZKpHPereDpXsNoGPC754FfGT0,3822
fairseq/examples/speech_recognition/infer.py,sha256=wf2s-kNNyRR10Gby447nxjGq0EvFDHFw9Km7FaZzJfQ,14677
fairseq/examples/speech_recognition/kaldi/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
fairseq/examples/speech_recognition/kaldi/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/speech_recognition/kaldi/__pycache__/kaldi_decoder.cpython-311.pyc,,
fairseq/examples/speech_recognition/kaldi/__pycache__/kaldi_initializer.cpython-311.pyc,,
fairseq/examples/speech_recognition/kaldi/add-self-loop-simple.cc,sha256=eQo9eYb9AZ_A8ktEeXCpZgYl4SGA8SMFtYKWxCvoprI,2866
fairseq/examples/speech_recognition/kaldi/config/kaldi_initializer.yaml,sha256=59mgXsJY4Z2vEd0sYPHf9NwRmIIttXgzms_H0uxjBDE,109
fairseq/examples/speech_recognition/kaldi/kaldi_decoder.py,sha256=U8K4_tpxrg6lagrJkZXk1iUK5WDUg-6CZ6yFFBpCE_Q,8265
fairseq/examples/speech_recognition/kaldi/kaldi_initializer.py,sha256=poFGoOWNz1-sz2R_COyyMaYtl-wm_NXlZyK_cBsqvbM,23441
fairseq/examples/speech_recognition/models/__init__.py,sha256=UV-quDbZnfbk2MylKZ-LPLEQWeegbgSxIXiDQ1JCR-0,276
fairseq/examples/speech_recognition/models/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/speech_recognition/models/__pycache__/vggtransformer.cpython-311.pyc,,
fairseq/examples/speech_recognition/models/__pycache__/w2l_conv_glu_enc.cpython-311.pyc,,
fairseq/examples/speech_recognition/models/vggtransformer.py,sha256=LHSPSJL0J-sHPwlVX1f1bdENoDVsSg9CZj5e9RuFm5Q,37260
fairseq/examples/speech_recognition/models/w2l_conv_glu_enc.py,sha256=ekQJCjqVEvSjGdTs-TSMOhBMY0pI1L6V8sKS8gQYyq4,6078
fairseq/examples/speech_recognition/new/README.md,sha256=9nFwrepFOKpWtlWzHtA7kVzVVLhoeRgZQwl09D5fsew,1107
fairseq/examples/speech_recognition/new/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
fairseq/examples/speech_recognition/new/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/speech_recognition/new/__pycache__/infer.cpython-311.pyc,,
fairseq/examples/speech_recognition/new/conf/hydra/sweeper/ax.yaml,sha256=tFykL-HGXKwfORAcWdNMXoqUvfHqaIjyCORA-ZVS7Xo,673
fairseq/examples/speech_recognition/new/conf/infer.yaml,sha256=kMcukLpuXW304zJ8GetOPlNvcLD3L0V_bf9lVcOx5pw,477
fairseq/examples/speech_recognition/new/decoders/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
fairseq/examples/speech_recognition/new/decoders/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/speech_recognition/new/decoders/__pycache__/base_decoder.cpython-311.pyc,,
fairseq/examples/speech_recognition/new/decoders/__pycache__/decoder.cpython-311.pyc,,
fairseq/examples/speech_recognition/new/decoders/__pycache__/decoder_config.cpython-311.pyc,,
fairseq/examples/speech_recognition/new/decoders/__pycache__/flashlight_decoder.cpython-311.pyc,,
fairseq/examples/speech_recognition/new/decoders/__pycache__/viterbi_decoder.cpython-311.pyc,,
fairseq/examples/speech_recognition/new/decoders/base_decoder.py,sha256=dcMsqotCb_boBZbC9kLOosSC8kh6KnudB1vjI6d0XLI,2093
fairseq/examples/speech_recognition/new/decoders/decoder.py,sha256=YUqyb2K3cqHI7IITzOFEm6sJJfcFb5wfj7UmAOiryzM,944
fairseq/examples/speech_recognition/new/decoders/decoder_config.py,sha256=nhHFwmNSoGqX6cxZjI5FBAR1PuQqrc8mAwy-VyS7a0I,2004
fairseq/examples/speech_recognition/new/decoders/flashlight_decoder.py,sha256=psFjHB4EtiNTVurIFGfeCcnHFw-ysnUIomvcOAOhwgw,14746
fairseq/examples/speech_recognition/new/decoders/viterbi_decoder.py,sha256=eNgcp3MKfWOa9v8Ervhi4Sjboq2GjD4xGFol5Z9UhVA,641
fairseq/examples/speech_recognition/new/infer.py,sha256=GIDhkR4vfExVtcgyjahU5Bb4um9YLlbSLiF6zfkD70E,16597
fairseq/examples/speech_recognition/tasks/__init__.py,sha256=oNFsSGudW6vYjJecsx5-FlbSS9qtUIu1suEEbgNvh5I,273
fairseq/examples/speech_recognition/tasks/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/speech_recognition/tasks/__pycache__/speech_recognition.cpython-311.pyc,,
fairseq/examples/speech_recognition/tasks/speech_recognition.py,sha256=FLiWBi5ST_0vXsW_-HgeduCEoI2ADJKED_o7hTaXu2U,5397
fairseq/examples/speech_recognition/utils/__pycache__/wer_utils.cpython-311.pyc,,
fairseq/examples/speech_recognition/utils/wer_utils.py,sha256=8Ul3sE7dSwmxAZOOeQuccqqliAxnEa_AC2e17jJBF8E,11842
fairseq/examples/speech_recognition/w2l_decoder.py,sha256=8Yw9bM7n5hI6V0zDkAcWBrCIH3LOpJFZt-VGvuH8kUY,17396
fairseq/examples/speech_synthesis/README.md,sha256=UfHmVEUFYUX_8N0VnmvsFKrnhGifMC83l2N96LIBMfM,1273
fairseq/examples/speech_synthesis/__init__.py,sha256=3wjgyxS71cYZY22CWXn8ZHBa-mxU_5cLQBACfS6xzoM,177
fairseq/examples/speech_synthesis/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/speech_synthesis/__pycache__/data_utils.cpython-311.pyc,,
fairseq/examples/speech_synthesis/__pycache__/generate_waveform.cpython-311.pyc,,
fairseq/examples/speech_synthesis/__pycache__/utils.cpython-311.pyc,,
fairseq/examples/speech_synthesis/data_utils.py,sha256=ihIZIGT3fNzMhS6lSNxluOwk39JGm0mhjWpAbvS-6vw,11364
fairseq/examples/speech_synthesis/docs/common_voice_example.md,sha256=TZQDdtS31XQKqiqikr2Z_iiPiIaoD2GnPa9izhdFmuo,2689
fairseq/examples/speech_synthesis/docs/ljspeech_example.md,sha256=c8PH4jhk5i0izxqmNqgu2h6L3R79t4xcwB2-0TbQAlk,6144
fairseq/examples/speech_synthesis/docs/vctk_example.md,sha256=Y0K7l2xmzdF_KOWXWUlyTcwgsE5Wj-lA0cJSd72djvw,2410
fairseq/examples/speech_synthesis/evaluation/__init__.py,sha256=3wjgyxS71cYZY22CWXn8ZHBa-mxU_5cLQBACfS6xzoM,177
fairseq/examples/speech_synthesis/evaluation/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/speech_synthesis/evaluation/__pycache__/eval_asr.cpython-311.pyc,,
fairseq/examples/speech_synthesis/evaluation/__pycache__/eval_f0.cpython-311.pyc,,
fairseq/examples/speech_synthesis/evaluation/__pycache__/eval_sp.cpython-311.pyc,,
fairseq/examples/speech_synthesis/evaluation/__pycache__/get_eval_manifest.cpython-311.pyc,,
fairseq/examples/speech_synthesis/evaluation/eval_asr.py,sha256=Xddm-FAWZrz8-Cv8psO5D9NKbGOX8Lt36-cqBS0eNCI,4654
fairseq/examples/speech_synthesis/evaluation/eval_f0.py,sha256=Tij9vjXO0VgbERn8HVSwNXzZMMgGqHbOL2rQIEDMJpI,8334
fairseq/examples/speech_synthesis/evaluation/eval_sp.py,sha256=PD6-CP74EsK98-C2B04zPaGZ-KlKjjn_KxbIw9pMdl0,4149
fairseq/examples/speech_synthesis/evaluation/get_eval_manifest.py,sha256=XjsofRA8Py2pTYyrAIkzZfeP-X07VofYlAEALqJj9_A,2268
fairseq/examples/speech_synthesis/generate_waveform.py,sha256=S6tI042HjfRRkB5enACF1UvPakOf15TOqpURXXJhGZM,7339
fairseq/examples/speech_synthesis/preprocessing/__init__.py,sha256=3wjgyxS71cYZY22CWXn8ZHBa-mxU_5cLQBACfS6xzoM,177
fairseq/examples/speech_synthesis/preprocessing/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/speech_synthesis/preprocessing/__pycache__/denoise_and_vad_audio.cpython-311.pyc,,
fairseq/examples/speech_synthesis/preprocessing/__pycache__/get_common_voice_audio_manifest.cpython-311.pyc,,
fairseq/examples/speech_synthesis/preprocessing/__pycache__/get_feature_manifest.cpython-311.pyc,,
fairseq/examples/speech_synthesis/preprocessing/__pycache__/get_ljspeech_audio_manifest.cpython-311.pyc,,
fairseq/examples/speech_synthesis/preprocessing/__pycache__/get_speaker_embedding.cpython-311.pyc,,
fairseq/examples/speech_synthesis/preprocessing/__pycache__/get_vctk_audio_manifest.cpython-311.pyc,,
fairseq/examples/speech_synthesis/preprocessing/denoise_and_vad_audio.py,sha256=paTyxoeJC3qpSvsGmT4zVR8NsAMwQMXSdANDi7fP3R0,7655
fairseq/examples/speech_synthesis/preprocessing/denoiser/__init__.py,sha256=3wjgyxS71cYZY22CWXn8ZHBa-mxU_5cLQBACfS6xzoM,177
fairseq/examples/speech_synthesis/preprocessing/denoiser/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/speech_synthesis/preprocessing/denoiser/__pycache__/demucs.cpython-311.pyc,,
fairseq/examples/speech_synthesis/preprocessing/denoiser/__pycache__/pretrained.cpython-311.pyc,,
fairseq/examples/speech_synthesis/preprocessing/denoiser/__pycache__/resample.cpython-311.pyc,,
fairseq/examples/speech_synthesis/preprocessing/denoiser/__pycache__/utils.cpython-311.pyc,,
fairseq/examples/speech_synthesis/preprocessing/denoiser/demucs.py,sha256=PY4k41dyNgJB7nEOKtW6nUZMCnW2QrSfejyIzLOnbMk,16989
fairseq/examples/speech_synthesis/preprocessing/denoiser/pretrained.py,sha256=m1W505m9nMeWHNy0zY-ly1xw34nZse0PLlFTUKeeAwo,2384
fairseq/examples/speech_synthesis/preprocessing/denoiser/resample.py,sha256=Dr_YAFM0GRdiDnwH3rv46Jf59P3b7DTJr4F4c26bw6c,2226
fairseq/examples/speech_synthesis/preprocessing/denoiser/utils.py,sha256=YcgsdENH7W6faGh3yITXZ0-0xI_oDa9xtN7mBY8JmJA,4770
fairseq/examples/speech_synthesis/preprocessing/get_common_voice_audio_manifest.py,sha256=k75rrWbLsOQvxCItlRX7YYyFrEbL0RgHxCxryU01Gzs,5277
fairseq/examples/speech_synthesis/preprocessing/get_feature_manifest.py,sha256=RlbOOJUr8iD_A1BuOiQn_KfZYF3jZFlgyhKikJHX6gU,11171
fairseq/examples/speech_synthesis/preprocessing/get_ljspeech_audio_manifest.py,sha256=gBaHhOIiMlmMBtetRZqkTBzzJsGsS_ipDn5envXGMV4,2288
fairseq/examples/speech_synthesis/preprocessing/get_speaker_embedding.py,sha256=xgCHlmwcU1-RHSxsGincz6Pbegmr0pyIflEojJAnth4,3116
fairseq/examples/speech_synthesis/preprocessing/get_vctk_audio_manifest.py,sha256=kUY5tFh_eTlui52n1C4uPz1ObgZWYXiVIgFrU_vDok4,2685
fairseq/examples/speech_synthesis/preprocessing/speaker_embedder/__init__.py,sha256=bQuavYuOvUhGSfNWS5OwGc0JSdrl_0zbOJNfeamavXI,4103
fairseq/examples/speech_synthesis/preprocessing/speaker_embedder/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/speech_synthesis/preprocessing/vad/__init__.py,sha256=gy34mmF9ymESCW4R2OLYGBanexyAoJOWoEJH-N6daSU,7372
fairseq/examples/speech_synthesis/preprocessing/vad/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/speech_synthesis/utils.py,sha256=kSealhsg95Dh46dyWKfpmrl1aJyMmupK5eUsmZ2bTBU,3357
fairseq/examples/speech_text_joint_to_text/README.md,sha256=rh2KzLD33lu8uokTf-GE0EBC5GDv6nHkiOe_S-TpaoM,2360
fairseq/examples/speech_text_joint_to_text/__init__.py,sha256=Eei-3puK7by9AJZ7Otx53TYQyQfXj8Fns25sieS9FBc,226
fairseq/examples/speech_text_joint_to_text/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/speech_text_joint_to_text/configs/mustc_noise.list,sha256=tQPZQbPU-gwp-1mkFzAbmwqIrKr6yja5Z9lZSsGqw_Q,776
fairseq/examples/speech_text_joint_to_text/criterions/__init__.py,sha256=tuat8GgHX3fNzkGlxIWAn1Em1Z5mhUQz1UVCnHsse0M,487
fairseq/examples/speech_text_joint_to_text/criterions/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/speech_text_joint_to_text/criterions/__pycache__/multi_modality_compound.cpython-311.pyc,,
fairseq/examples/speech_text_joint_to_text/criterions/__pycache__/multi_modality_cross_entropy.cpython-311.pyc,,
fairseq/examples/speech_text_joint_to_text/criterions/__pycache__/text_guide_cross_entropy_acc.cpython-311.pyc,,
fairseq/examples/speech_text_joint_to_text/criterions/multi_modality_compound.py,sha256=I3siXYDqkkGBgcQmFJelNWVhaLZKSL8b-ODDcUwWj2Y,6486
fairseq/examples/speech_text_joint_to_text/criterions/multi_modality_cross_entropy.py,sha256=Fa9y8UUr5t6c0LIXMykPmgfEtXkwN2GLc9bhafCjJ7M,4105
fairseq/examples/speech_text_joint_to_text/criterions/text_guide_cross_entropy_acc.py,sha256=d9ZlS3WzhpOj8v700oXQweyxbnBhHtAZnXeuXeHsnK0,11004
fairseq/examples/speech_text_joint_to_text/data/__pycache__/pair_denoising_dataset.cpython-311.pyc,,
fairseq/examples/speech_text_joint_to_text/data/pair_denoising_dataset.py,sha256=Isn1qZD4D6JgPBSMp8PajXU-gStPhllq6PhnysGrVd8,11448
fairseq/examples/speech_text_joint_to_text/docs/ende-mustc.md,sha256=vGjHJQZ3GGBlSE1xON_MJLY3a2U50_qXDN-srcbS9s4,7463
fairseq/examples/speech_text_joint_to_text/docs/iwslt2021.md,sha256=XpgdsqnNf6idplaQNipVd0vEYXwFjESOMIuNPgYSQvE,3745
fairseq/examples/speech_text_joint_to_text/docs/pre-training.md,sha256=pjTePrGS5894iW7QbTVLqNq535k74TqjQO-8FMuSuHs,16890
fairseq/examples/speech_text_joint_to_text/models/__init__.py,sha256=5j2DAy3ybHRxIxVWTulUXX8Bvd6kHGNqn947rCnMDz4,206
fairseq/examples/speech_text_joint_to_text/models/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/speech_text_joint_to_text/models/__pycache__/joint_speech_text_pretrain_transformer.cpython-311.pyc,,
fairseq/examples/speech_text_joint_to_text/models/__pycache__/s2t_dualinputtransformer.cpython-311.pyc,,
fairseq/examples/speech_text_joint_to_text/models/__pycache__/s2t_dualinputwavtransformer.cpython-311.pyc,,
fairseq/examples/speech_text_joint_to_text/models/__pycache__/s2t_dualinputxmtransformer.cpython-311.pyc,,
fairseq/examples/speech_text_joint_to_text/models/joint_speech_text_pretrain_transformer.py,sha256=lbf-CCFFtu05xTjN8CVNe5k9SUBmX7KGDyDithAfrb8,28546
fairseq/examples/speech_text_joint_to_text/models/s2t_dualinputtransformer.py,sha256=mTCjdUtencZ1clHScwcoJ0-mAn4zqRMU-WINtH9pGo4,45047
fairseq/examples/speech_text_joint_to_text/models/s2t_dualinputwavtransformer.py,sha256=STeZztndg8ZBmDd6mJkvMHPy4Us9xMZd69Su140rhwo,21145
fairseq/examples/speech_text_joint_to_text/models/s2t_dualinputxmtransformer.py,sha256=x94dM2JygKLTdGyiAjDAfN9saIqKFC9Zxy5l4kg5axo,21461
fairseq/examples/speech_text_joint_to_text/scripts/__pycache__/convert_model.cpython-311.pyc,,
fairseq/examples/speech_text_joint_to_text/scripts/__pycache__/g2p_encode.cpython-311.pyc,,
fairseq/examples/speech_text_joint_to_text/scripts/convert_model.py,sha256=jP4qWNjmInhb77oxbFLmYOIE0HjAMOFL4FqtNPSTLLM,1866
fairseq/examples/speech_text_joint_to_text/scripts/g2p_encode.py,sha256=7Tx19Npf1DSBermZw6N_QX4JG6-xcmK3ZCWGKPhdfFE,5844
fairseq/examples/speech_text_joint_to_text/tasks/__init__.py,sha256=5j2DAy3ybHRxIxVWTulUXX8Bvd6kHGNqn947rCnMDz4,206
fairseq/examples/speech_text_joint_to_text/tasks/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/speech_text_joint_to_text/tasks/__pycache__/pair_denoising.cpython-311.pyc,,
fairseq/examples/speech_text_joint_to_text/tasks/__pycache__/speech_text_denoise_pretrain.cpython-311.pyc,,
fairseq/examples/speech_text_joint_to_text/tasks/__pycache__/speech_text_joint.cpython-311.pyc,,
fairseq/examples/speech_text_joint_to_text/tasks/pair_denoising.py,sha256=ietytBn7NWJ1HouwJnBChXrQzXLK1vH9PoC-SGM_qW4,15550
fairseq/examples/speech_text_joint_to_text/tasks/speech_text_denoise_pretrain.py,sha256=KUZQUraEwRugRw9SjkaoyWvuq8FGACK33mBJi1Ifcrw,25475
fairseq/examples/speech_text_joint_to_text/tasks/speech_text_joint.py,sha256=mB-JTaau64bByCHP6nd2ImK6pb33R9CkKpGJIIVdOjs,13654
fairseq/examples/speech_to_speech/README.md,sha256=_P7kQjdA7o45W_mytF75Fnqh_UjwJQK2yfaaF1GOikA,518
fairseq/examples/speech_to_speech/__init__.py,sha256=3wjgyxS71cYZY22CWXn8ZHBa-mxU_5cLQBACfS6xzoM,177
fairseq/examples/speech_to_speech/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/speech_to_speech/__pycache__/generate_waveform_from_code.cpython-311.pyc,,
fairseq/examples/speech_to_speech/benchmarking/README.md,sha256=sFrlN0ofy42ugyMo_cBfJdvM7M5Wn-sXRt7odUMr9d8,2288
fairseq/examples/speech_to_speech/benchmarking/__pycache__/core.cpython-311.pyc,,
fairseq/examples/speech_to_speech/benchmarking/__pycache__/data_utils.cpython-311.pyc,,
fairseq/examples/speech_to_speech/benchmarking/__pycache__/get_metrics.cpython-311.pyc,,
fairseq/examples/speech_to_speech/benchmarking/configs/2StageS2ST.yaml,sha256=yvKFqVhtjuNIxOoikCxi_fsGHhMmeL2DS7Fis3AK_oc,347
fairseq/examples/speech_to_speech/benchmarking/configs/3StageS2ST.yaml,sha256=LC-kVIBhEJud-uZmrDgf1QmgYhqU0YF17hgn72PDWOI,488
fairseq/examples/speech_to_speech/benchmarking/configs/DirectS2U.yaml,sha256=6MPYcRyQNhlo5LP_DQaXazJaX7YPjCsbUjUrPrG6jAE,443
fairseq/examples/speech_to_speech/benchmarking/configs/S2T.yaml,sha256=7VOwdDV80sntA_SOpApF7AqPVIy-2xGejylrm5jx-8E,215
fairseq/examples/speech_to_speech/benchmarking/core.py,sha256=7D3hehS0a_QALMQVlBQMg8VwxE1-Wq7UrVJzul_0HOY,17782
fairseq/examples/speech_to_speech/benchmarking/data_utils.py,sha256=SiGOIhZL56w8b7RylVErhQtLObK3vIB75N9hep9d4d0,7893
fairseq/examples/speech_to_speech/benchmarking/get_metrics.py,sha256=aaYzeYSdZ96FfnOkMFaz23A7ta-GI8MmNNQA2NMBq1E,5053
fairseq/examples/speech_to_speech/docs/direct_s2st_discrete_units.md,sha256=kMO-E9He7HE-LVEe5F-aIVgpAA-z6zhFoWHPpMbb4cc,10498
fairseq/examples/speech_to_speech/docs/enhanced_direct_s2st_discrete_units.md,sha256=r4P8TZd6wuat3hDZVvqRjoJR2LISysbRs23hEnFMdGQ,6584
fairseq/examples/speech_to_speech/docs/textless_s2st_real_data.md,sha256=rZAbzyD2BhWAa5YSY6q-3ebeVX0HJ09B-8UejCUIZFU,5289
fairseq/examples/speech_to_speech/generate_waveform_from_code.py,sha256=ikg8LpGYGkJj4DjGbM8_owXSzuqQHgt7BxCAmKe80vc,3285
fairseq/examples/speech_to_speech/preprocessing/__init__.py,sha256=3wjgyxS71cYZY22CWXn8ZHBa-mxU_5cLQBACfS6xzoM,177
fairseq/examples/speech_to_speech/preprocessing/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/speech_to_speech/preprocessing/__pycache__/data_utils.cpython-311.pyc,,
fairseq/examples/speech_to_speech/preprocessing/__pycache__/prep_s2spect_data.cpython-311.pyc,,
fairseq/examples/speech_to_speech/preprocessing/__pycache__/prep_s2ut_data.cpython-311.pyc,,
fairseq/examples/speech_to_speech/preprocessing/__pycache__/prep_sn_data.cpython-311.pyc,,
fairseq/examples/speech_to_speech/preprocessing/__pycache__/prep_sn_output_data.cpython-311.pyc,,
fairseq/examples/speech_to_speech/preprocessing/data_utils.py,sha256=wDJHDJK8Qr73feL0hh_N7eQ0Yuss1z0tP7W9--nfcgY,2592
fairseq/examples/speech_to_speech/preprocessing/prep_s2spect_data.py,sha256=D_sa9XGfWu8Qe4tWgQBvG3dOlPx7qjllE-KPtfzvRhY,5844
fairseq/examples/speech_to_speech/preprocessing/prep_s2ut_data.py,sha256=-HmWGehKBZmt_coQM7YFytUTEjNLw8tclBv95sBEPWo,3554
fairseq/examples/speech_to_speech/preprocessing/prep_sn_data.py,sha256=foK8PXSZdPrRWxwKT6wo_6eVd56NNBhhcuKlRjPGCB0,2735
fairseq/examples/speech_to_speech/preprocessing/prep_sn_output_data.py,sha256=3-fWv5aS3xXf9W1EJPbTAvZf3QxK1DEtLRsVxGcr-Lk,1455
fairseq/examples/speech_to_text/README.md,sha256=btavBcSheT1vHSXHlmHLNfBAzjenuTr64yAz9ucVz_s,4479
fairseq/examples/speech_to_text/__pycache__/data_utils.cpython-311.pyc,,
fairseq/examples/speech_to_text/__pycache__/prep_covost_data.cpython-311.pyc,,
fairseq/examples/speech_to_text/__pycache__/prep_librispeech_data.cpython-311.pyc,,
fairseq/examples/speech_to_text/__pycache__/prep_mtedx_data.cpython-311.pyc,,
fairseq/examples/speech_to_text/__pycache__/prep_mustc_data.cpython-311.pyc,,
fairseq/examples/speech_to_text/__pycache__/seg_mustc_data.cpython-311.pyc,,
fairseq/examples/speech_to_text/data_utils.py,sha256=iMuPrpgEr9aij5wXhEs9nz6bJ2AYnylIa9fL4wDip28,12272
fairseq/examples/speech_to_text/docs/covost_example.md,sha256=JFXzppeHrGXCMQHVicJklFj4XFFJPUnqSQCGgfjm0t0,13297
fairseq/examples/speech_to_text/docs/librispeech_example.md,sha256=g4H2pQxfNjz5ZVwFFbrfdJhj4wT5_EAPoYpBxTTSIc4,3318
fairseq/examples/speech_to_text/docs/mtedx_example.md,sha256=qv831MeFb5gKxTetmHoD21odYgccI-uj4wGfx9v6hto,10199
fairseq/examples/speech_to_text/docs/mustc_example.md,sha256=t6PI_dq18qtOs8wT2cJWR2JN1rhLx-jdE1htQg_5ZRE,10856
fairseq/examples/speech_to_text/docs/simulst_mustc_example.md,sha256=ddt681gviqv2GjpI5spTqOpCe3hamkESi4qrQP952Yg,7770
fairseq/examples/speech_to_text/prep_covost_data.py,sha256=iXmp5w_0PIjdfL10i1DgEl6hrOkqHujk1TIboAP8czE,8909
fairseq/examples/speech_to_text/prep_librispeech_data.py,sha256=3f3yt8b74hIEdTu1ixT1oTiFOAPRutOj8wT0ADt6Yj0,3623
fairseq/examples/speech_to_text/prep_mtedx_data.py,sha256=mhPtDbuRiNfKojU9Sr9ggwJSO1rKUHqoI6046RzFq00,10168
fairseq/examples/speech_to_text/prep_mustc_data.py,sha256=IaFqHzR8LK2Dvuo2AKtA0hEEHazA9vMke_uqb_V9W9A,11080
fairseq/examples/speech_to_text/seg_mustc_data.py,sha256=fJE20xKjpFFyh9XhOn-_t001RBFhuZb_l2FBXzPVn3M,1645
fairseq/examples/speech_to_text/simultaneous_translation/agents/__pycache__/fairseq_simul_st_agent.cpython-311.pyc,,
fairseq/examples/speech_to_text/simultaneous_translation/agents/fairseq_simul_st_agent.py,sha256=y4Nm2F0IJUhY7MX4PMjBYr1DzF5v0jhXTgXYMX-Wz5k,12193
fairseq/examples/stories/README.md,sha256=5kDN_V7VaxieRo1bithTOIvF9WslQRtYCcA_H-hDElM,4145
fairseq/examples/textless_nlp/gslm/README.md,sha256=ESFUTMSNIJi4mvT7mTXLPBDVdO_GcJGeHZd--ElR97U,1425
fairseq/examples/textless_nlp/gslm/metrics/README.md,sha256=TaEeffYJZ0WCPg6nRQV1zS07T6EEqPS0RhpdjoJJskE,715
fairseq/examples/textless_nlp/gslm/metrics/abx_metrics/README.md,sha256=n4etFap-kueTfXtPdo8TX1Gmw-rDURVaDx8INlfwm_U,2813
fairseq/examples/textless_nlp/gslm/metrics/abx_metrics/__pycache__/dump_abx_feats.cpython-311.pyc,,
fairseq/examples/textless_nlp/gslm/metrics/abx_metrics/dump_abx_feats.py,sha256=E4ZjTsu6LojYAOuKONlx-2CxSSqK7BgXTKcvHJoecrM,3329
fairseq/examples/textless_nlp/gslm/metrics/asr_metrics/README.md,sha256=FsR6Y_bRCXNToWCi462WlrRdrMbwT31CB_x0Sx7mwf4,5345
fairseq/examples/textless_nlp/gslm/metrics/asr_metrics/__pycache__/continuation_eval.cpython-311.pyc,,
fairseq/examples/textless_nlp/gslm/metrics/asr_metrics/__pycache__/ppx.cpython-311.pyc,,
fairseq/examples/textless_nlp/gslm/metrics/asr_metrics/__pycache__/self_auto_bleu.cpython-311.pyc,,
fairseq/examples/textless_nlp/gslm/metrics/asr_metrics/continuation_eval.py,sha256=m2y0V9-HoVTzBgV6gKQiOmog6Rg5elYjk5jEPsoaGmc,2869
fairseq/examples/textless_nlp/gslm/metrics/asr_metrics/misc/__pycache__/bleu_utils.cpython-311.pyc,,
fairseq/examples/textless_nlp/gslm/metrics/asr_metrics/misc/__pycache__/cut_as.cpython-311.pyc,,
fairseq/examples/textless_nlp/gslm/metrics/asr_metrics/misc/bleu_utils.py,sha256=HUL9NDlspbaMKsrAHQeb73gZ2g3deufWKVz8gvySErg,6679
fairseq/examples/textless_nlp/gslm/metrics/asr_metrics/misc/cut_as.py,sha256=F-5bMcbwpxp5053fhf2Ny7G2zbnoBnxbtu_kv5R-JNE,1832
fairseq/examples/textless_nlp/gslm/metrics/asr_metrics/misc/dict.ltr.txt,sha256=kweXH71QdmW-oD4slR44_VeyMzXfBVkHS_7ykI__8Jk,207
fairseq/examples/textless_nlp/gslm/metrics/asr_metrics/ppx.py,sha256=QPilnc_byAjNcBqOwlLsNvJMPHeDqcJ-x_CZBm-4lPw,3692
fairseq/examples/textless_nlp/gslm/metrics/asr_metrics/self_auto_bleu.py,sha256=j8Uqlk2FyH2789-x7qUcU7GtjcRCmLb0v4-uS3Ris_s,6101
fairseq/examples/textless_nlp/gslm/speech2unit/README.md,sha256=Rgium3FS59ujAY3nfakbKcFbzVFiQBeZWhDXr04pe04,3468
fairseq/examples/textless_nlp/gslm/speech2unit/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
fairseq/examples/textless_nlp/gslm/speech2unit/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/textless_nlp/gslm/speech2unit/clustering/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
fairseq/examples/textless_nlp/gslm/speech2unit/clustering/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/textless_nlp/gslm/speech2unit/clustering/__pycache__/cluster_kmeans.cpython-311.pyc,,
fairseq/examples/textless_nlp/gslm/speech2unit/clustering/__pycache__/dump_feats.cpython-311.pyc,,
fairseq/examples/textless_nlp/gslm/speech2unit/clustering/__pycache__/quantize_with_kmeans.cpython-311.pyc,,
fairseq/examples/textless_nlp/gslm/speech2unit/clustering/__pycache__/utils.cpython-311.pyc,,
fairseq/examples/textless_nlp/gslm/speech2unit/clustering/cluster_kmeans.py,sha256=wH422euh4MfAQNfnJCSGb73RBtnmeCvD0KeKp8JinvE,6182
fairseq/examples/textless_nlp/gslm/speech2unit/clustering/dump_feats.py,sha256=dmtk7D2hJRf31aDXkCVE8xqa06uw4Eb73cztHJrkzyk,2615
fairseq/examples/textless_nlp/gslm/speech2unit/clustering/quantize_with_kmeans.py,sha256=W1I1ymgnnVGYNFCVViXfGPAX6hFCA95AOE_MDAKQHPI,3821
fairseq/examples/textless_nlp/gslm/speech2unit/clustering/utils.py,sha256=WpMTi0yhdMU6x4HZHTmI1bE5I5VwdUzQ3ApCM5VL2bs,701
fairseq/examples/textless_nlp/gslm/speech2unit/pretrained/__pycache__/cpc_feature_reader.cpython-311.pyc,,
fairseq/examples/textless_nlp/gslm/speech2unit/pretrained/__pycache__/hubert_feature_reader.cpython-311.pyc,,
fairseq/examples/textless_nlp/gslm/speech2unit/pretrained/__pycache__/logmel_feature_reader.cpython-311.pyc,,
fairseq/examples/textless_nlp/gslm/speech2unit/pretrained/__pycache__/utils.cpython-311.pyc,,
fairseq/examples/textless_nlp/gslm/speech2unit/pretrained/__pycache__/w2v2_feature_reader.cpython-311.pyc,,
fairseq/examples/textless_nlp/gslm/speech2unit/pretrained/cpc_feature_reader.py,sha256=7zczqOd82AdEVUtn-L4WwCyexlVwramaJwThli2l_m4,6525
fairseq/examples/textless_nlp/gslm/speech2unit/pretrained/hubert_feature_reader.py,sha256=IuRjZx-MvdNeq1Ck9yA-cyW2lnXcm6JApmw9-aZRJHU,1912
fairseq/examples/textless_nlp/gslm/speech2unit/pretrained/logmel_feature_reader.py,sha256=FAwHpCYqrOoDBnrLPsoatiunf3Ey1WhCLaynrSYNBks,905
fairseq/examples/textless_nlp/gslm/speech2unit/pretrained/utils.py,sha256=YyN1-pLh6YD5Cb2iGW9nicyMwiPSDJSwb-N6otZX4NM,3407
fairseq/examples/textless_nlp/gslm/speech2unit/pretrained/w2v2_feature_reader.py,sha256=S25l9QyLgA0ybByPYxpuD5ZITNPRh8NSH6NbPQ2cePU,1424
fairseq/examples/textless_nlp/gslm/tools/README.md,sha256=cLj6zUSIeHtvaK3NVGs6sSWn4SddJ5H64tp9AtlkdMg,1168
fairseq/examples/textless_nlp/gslm/tools/__pycache__/resynthesize_speech.cpython-311.pyc,,
fairseq/examples/textless_nlp/gslm/tools/resynthesize_speech.py,sha256=6KEnYXemsAO93YDSIMycJL17KEGLCe95gP48sUCdSqY,4183
fairseq/examples/textless_nlp/gslm/ulm/README.md,sha256=ulDl_crk_qxwCZnzHcOWUIAmFR0HMa21TvZ89pNPI94,5040
fairseq/examples/textless_nlp/gslm/ulm/__pycache__/sample.cpython-311.pyc,,
fairseq/examples/textless_nlp/gslm/ulm/sample.py,sha256=_U8eAw6U-Ru9KK7X9kwDAVMAzZp-08mSo7hiTOBf0NQ,5623
fairseq/examples/textless_nlp/gslm/unit2speech/README.md,sha256=fMzojiLavDI8XOE89ARP7BY5XC4TMaEMx232Xas44EM,4407
fairseq/examples/textless_nlp/gslm/unit2speech/__pycache__/convert_to_16k.cpython-311.pyc,,
fairseq/examples/textless_nlp/gslm/unit2speech/__pycache__/glow.cpython-311.pyc,,
fairseq/examples/textless_nlp/gslm/unit2speech/__pycache__/multiproc.cpython-311.pyc,,
fairseq/examples/textless_nlp/gslm/unit2speech/__pycache__/synthesize_audio_from_units.cpython-311.pyc,,
fairseq/examples/textless_nlp/gslm/unit2speech/__pycache__/tts_data.cpython-311.pyc,,
fairseq/examples/textless_nlp/gslm/unit2speech/__pycache__/utils.cpython-311.pyc,,
fairseq/examples/textless_nlp/gslm/unit2speech/convert_to_16k.py,sha256=54SkJ5UcJ_UkL_5a3GoMNPxab7CG57X36TNAUz5K7Cg,2177
fairseq/examples/textless_nlp/gslm/unit2speech/glow.py,sha256=JXZfaxvVDvWizXBcWs5LHKcHE6TjgsJI3no09EN-JVo,12653
fairseq/examples/textless_nlp/gslm/unit2speech/multiproc.py,sha256=7EOMoeMfHF-093m5YgPhHgShZpg9npXOZpPj0kCMBGU,772
fairseq/examples/textless_nlp/gslm/unit2speech/synthesize_audio_from_units.py,sha256=xfEONNbluc41JfUMxXAXG5tLv-yw0JBoP28pYV3o58Q,3178
fairseq/examples/textless_nlp/gslm/unit2speech/tacotron2/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
fairseq/examples/textless_nlp/gslm/unit2speech/tacotron2/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/textless_nlp/gslm/unit2speech/tacotron2/__pycache__/audio_processing.cpython-311.pyc,,
fairseq/examples/textless_nlp/gslm/unit2speech/tacotron2/__pycache__/cleaners.cpython-311.pyc,,
fairseq/examples/textless_nlp/gslm/unit2speech/tacotron2/__pycache__/cmudict.cpython-311.pyc,,
fairseq/examples/textless_nlp/gslm/unit2speech/tacotron2/__pycache__/layers.cpython-311.pyc,,
fairseq/examples/textless_nlp/gslm/unit2speech/tacotron2/__pycache__/model.cpython-311.pyc,,
fairseq/examples/textless_nlp/gslm/unit2speech/tacotron2/__pycache__/numbers.cpython-311.pyc,,
fairseq/examples/textless_nlp/gslm/unit2speech/tacotron2/__pycache__/stft.cpython-311.pyc,,
fairseq/examples/textless_nlp/gslm/unit2speech/tacotron2/__pycache__/symbols.cpython-311.pyc,,
fairseq/examples/textless_nlp/gslm/unit2speech/tacotron2/__pycache__/text.cpython-311.pyc,,
fairseq/examples/textless_nlp/gslm/unit2speech/tacotron2/__pycache__/utils.cpython-311.pyc,,
fairseq/examples/textless_nlp/gslm/unit2speech/tacotron2/__pycache__/waveglow_denoiser.cpython-311.pyc,,
fairseq/examples/textless_nlp/gslm/unit2speech/tacotron2/audio_processing.py,sha256=Yo0z4-79vKFvCDbTGt5FZZwukOgoaWyR_uQyaLH5r0E,2610
fairseq/examples/textless_nlp/gslm/unit2speech/tacotron2/cleaners.py,sha256=ntNPqHbbRX0p6OhuhZOv86junLYidj03G4cEjj1_mLs,2439
fairseq/examples/textless_nlp/gslm/unit2speech/tacotron2/cmudict.py,sha256=m6awPpqrV9AI-HlGvQDk8VeoSE3wxjrk7cP1In_EXPU,1979
fairseq/examples/textless_nlp/gslm/unit2speech/tacotron2/layers.py,sha256=h1KEBrGmPhpcHQ3VW0fqjP9_4xIQ7Sqmlj__X_TU7vA,3859
fairseq/examples/textless_nlp/gslm/unit2speech/tacotron2/model.py,sha256=3Ykzg3_i0SEpZDynyswdCzBQ0rQfm1wFZMSaNW-n24Q,25989
fairseq/examples/textless_nlp/gslm/unit2speech/tacotron2/numbers.py,sha256=QccHV1nzbG6gWwQUsUuZdfH8S1djVjCPl4jElQhQ3HE,2168
fairseq/examples/textless_nlp/gslm/unit2speech/tacotron2/stft.py,sha256=-3Gg0qirFpR2dHgudMOrk19sJbGiF--Ruqqdk2s-AsE,5893
fairseq/examples/textless_nlp/gslm/unit2speech/tacotron2/symbols.py,sha256=r-egVFAW0NM4129uuwIiCNFr7Fyoc4XP0GP5uZHwu7U,718
fairseq/examples/textless_nlp/gslm/unit2speech/tacotron2/text.py,sha256=Xf3AZca9UsjYUgKQQsTNdBYwSefbAOdMTEEZ9gjFtNI,3075
fairseq/examples/textless_nlp/gslm/unit2speech/tacotron2/utils.py,sha256=gUm26oMMx6nEazl6t6TEVCbATqUO_4Uh58-gE_J5bSA,4918
fairseq/examples/textless_nlp/gslm/unit2speech/tacotron2/waveglow_denoiser.py,sha256=Z7msmJ28AM2CvZ7l-GnQ_ho57fxxqp3doePUEV7sAu4,1610
fairseq/examples/textless_nlp/gslm/unit2speech/tts_data.py,sha256=hFdjvG-xXfFf8aBkeLmKM6aId03h91uqIDN7pQXtYjY,1733
fairseq/examples/textless_nlp/gslm/unit2speech/utils.py,sha256=ZyMha8Xx3bacTjHPYGUrQ5vpzs9bNI4twhpTYw5N0m4,1904
fairseq/examples/textless_nlp/pgslm/README.md,sha256=oNQHjVVvsp-h7BCgTNGcxKColYHcPM500am-lxauXkU,17236
fairseq/examples/textless_nlp/pgslm/__pycache__/data_utils.cpython-311.pyc,,
fairseq/examples/textless_nlp/pgslm/__pycache__/generate_waveform.cpython-311.pyc,,
fairseq/examples/textless_nlp/pgslm/__pycache__/inference_dataset.cpython-311.pyc,,
fairseq/examples/textless_nlp/pgslm/__pycache__/naive_decoder.cpython-311.pyc,,
fairseq/examples/textless_nlp/pgslm/__pycache__/prepare_dataset.cpython-311.pyc,,
fairseq/examples/textless_nlp/pgslm/__pycache__/preprocess_f0.cpython-311.pyc,,
fairseq/examples/textless_nlp/pgslm/__pycache__/quantize_f0.cpython-311.pyc,,
fairseq/examples/textless_nlp/pgslm/__pycache__/truncated_laplace.cpython-311.pyc,,
fairseq/examples/textless_nlp/pgslm/data_utils.py,sha256=5va5U4C-jpa0XQJutvSiTzgLjafTXZFgflg838oETsk,2568
fairseq/examples/textless_nlp/pgslm/eval/__init__.py,sha256=aHA6xMaCvER4jyIXv_doV4bBAiGJXJDsBE9RrHNQqg8,176
fairseq/examples/textless_nlp/pgslm/eval/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/textless_nlp/pgslm/eval/__pycache__/cont_metrics.cpython-311.pyc,,
fairseq/examples/textless_nlp/pgslm/eval/cont_metrics.py,sha256=ga2hcx3d1xA_SYmaTA1qnPJ_zM9XH2qAJGR3gCIk4Fs,23650
fairseq/examples/textless_nlp/pgslm/generate_waveform.py,sha256=4s0EjjnfFTDu5eXkn_OOPnQCbNgoEo_QRC03TMqbmoc,3312
fairseq/examples/textless_nlp/pgslm/inference_dataset.py,sha256=R8os62AfZ-jmF6zuUr95Wb838mvRe1jEpv8eCXSzxfA,3068
fairseq/examples/textless_nlp/pgslm/naive_decoder.py,sha256=-yI06RoN5eCzefK3DbbDXdjjKW1_k13XSMm599K1zsA,1495
fairseq/examples/textless_nlp/pgslm/prepare_dataset.py,sha256=rUQRSzyOv04UnaL6qx2y2hb8_LfiqDYW6YY7FtdkeuY,4623
fairseq/examples/textless_nlp/pgslm/preprocess_f0.py,sha256=CBDmMOz49FnVOtSTBz3xwdpzTkYHO5bt64tH6ND1QF8,1872
fairseq/examples/textless_nlp/pgslm/quantize_f0.py,sha256=EjHJ-IL8W9GtjDy0ti76Vap63Du3MJTti_K7I1SM7Zk,3064
fairseq/examples/textless_nlp/pgslm/sample/__init__.py,sha256=aHA6xMaCvER4jyIXv_doV4bBAiGJXJDsBE9RrHNQqg8,176
fairseq/examples/textless_nlp/pgslm/sample/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/textless_nlp/pgslm/sample/__pycache__/sample.cpython-311.pyc,,
fairseq/examples/textless_nlp/pgslm/sample/sample.py,sha256=xaH4yAflIeGOSNdCxaM7Anm3Aq2b42c4-ZYIuxrlOpI,20977
fairseq/examples/textless_nlp/pgslm/scripts/__pycache__/join_units_manifest.cpython-311.pyc,,
fairseq/examples/textless_nlp/pgslm/scripts/join_units_manifest.py,sha256=MDkD4_mVdVJUAEgDvBrrrqOVmTpwqFumMl6C31_oO2Q,1430
fairseq/examples/textless_nlp/pgslm/scripts/prepare_data.sh,sha256=aySvoq9iJhfFMVtor4VRAdqzYXOWy_4nbw-ZOy__KAQ,1487
fairseq/examples/textless_nlp/pgslm/scripts/prepare_f0_quantization.sh,sha256=2VmpXtorIHKjlXdgFbcQFvDJbA9Lotolr2qEu2mWbEQ,824
fairseq/examples/textless_nlp/pgslm/truncated_laplace.py,sha256=7B4RUfYSF2fP9fvWoTWriseDyu_N-CCcKHupyD2TO4o,1060
fairseq/examples/textless_nlp/speech-resynth/README.md,sha256=Ii_4s1kZmVoQ3OIxAGp12V1jQR4aAqZJC8JwX1rfMHo,2115
fairseq/examples/textless_nlp/speech-resynth/img/fig.png,sha256=wZxXDzZx2IVR9dW5COJw5pvXXTBMXTWIaPoZ80KXnBc,307833
fairseq/examples/translation/README.md,sha256=4mI5E7pmFSkJZg8dCLYOMbaZTNTdKTOZ9uRjZy_53e8,14800
fairseq/examples/translation/prepare-iwslt14.sh,sha256=ntS9a0sQsmmXBzHOvuc-pTs21Bh43JbTIoBfcaOkDh0,2981
fairseq/examples/translation/prepare-iwslt17-multilingual.sh,sha256=sVO0FaKBlBg515qx99qZEOcwVJ5tGXGYCo3FdkXbOHQ,4386
fairseq/examples/translation/prepare-wmt14en2de.sh,sha256=00OC55hvb9rt5Wntx6XBVnDtRYOGf7hHQ29CO7OGWkE,3962
fairseq/examples/translation/prepare-wmt14en2fr.sh,sha256=opLIFeSpAmjxqccgABH36xH3fUKyzRIkjpmc4jFLtcQ,3724
fairseq/examples/translation_moe/README.md,sha256=nUUuHZXJ_b1xiqOhKQsbeVqhx-1Oy3hcUdVFPKn5cXw,3536
fairseq/examples/translation_moe/__pycache__/score.cpython-311.pyc,,
fairseq/examples/translation_moe/score.py,sha256=xdXqBoAALuYt-oZc7EEOM8XDJClxpO5Iftdc16UAOAE,6127
fairseq/examples/translation_moe/translation_moe_src/__init__.py,sha256=hVt17QbVzN_XalLwBrIJ5YEwrh_PE_Rg3ExAzS1df0g,216
fairseq/examples/translation_moe/translation_moe_src/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/translation_moe/translation_moe_src/__pycache__/logsumexp_moe.cpython-311.pyc,,
fairseq/examples/translation_moe/translation_moe_src/__pycache__/mean_pool_gating_network.cpython-311.pyc,,
fairseq/examples/translation_moe/translation_moe_src/__pycache__/translation_moe.cpython-311.pyc,,
fairseq/examples/translation_moe/translation_moe_src/logsumexp_moe.py,sha256=jRFtioGR7BNmwKWyZuzBJ2Lbfiz7wwYmqubEdStnfnE,837
fairseq/examples/translation_moe/translation_moe_src/mean_pool_gating_network.py,sha256=vUx3zPorGJioBScvaY8JuN4qvS9Qk3Agf3MwHsNbwRo,2011
fairseq/examples/translation_moe/translation_moe_src/translation_moe.py,sha256=NfOzkEIt7b6-8B7tAN8l2sOHSOQsMlZvK8fKNgQ92-k,9507
fairseq/examples/truncated_bptt/README.md,sha256=za5a2BmSNWwZZ6vEMAyyYVl7E8dBw92C6S1fVR7w4bA,3266
fairseq/examples/truncated_bptt/__init__.py,sha256=pX7KDt9vB10vqJ6CMmI89pH2HyHkXbp5dcPL1aQA3E0,245
fairseq/examples/truncated_bptt/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/truncated_bptt/__pycache__/transformer_xl_model.cpython-311.pyc,,
fairseq/examples/truncated_bptt/__pycache__/truncated_bptt_lm_task.cpython-311.pyc,,
fairseq/examples/truncated_bptt/transformer_xl_model.py,sha256=mPvg3xiWgnprZodw9oR-pSRWqhA8P5Sra9QvrKyAaKs,4738
fairseq/examples/truncated_bptt/truncated_bptt_lm_task.py,sha256=EWnIkcc_HK3FvtWLkKD__3gtfhuZFqV51YVL7UXx1do,9995
fairseq/examples/unsupervised_quality_estimation/README.md,sha256=p8jGtrWzKQOKOva7P6wUXxrrtXNBSSw9XN28DRicwtc,5089
fairseq/examples/unsupervised_quality_estimation/__pycache__/aggregate_scores.cpython-311.pyc,,
fairseq/examples/unsupervised_quality_estimation/__pycache__/meteor.cpython-311.pyc,,
fairseq/examples/unsupervised_quality_estimation/__pycache__/repeat_lines.cpython-311.pyc,,
fairseq/examples/unsupervised_quality_estimation/aggregate_scores.py,sha256=8FMp4iMMMchBLgk4mCNGW2BFgo5erHAnqeiMn8V70B0,1136
fairseq/examples/unsupervised_quality_estimation/meteor.py,sha256=NHFkI1MwhE2REs6DFcQoVTKzz2lj9Nr-xhpWP-LVnto,3472
fairseq/examples/unsupervised_quality_estimation/repeat_lines.py,sha256=XHxGuQbD3htF59NPfQha3PxekQyPOnxuwGBAKlHKIsM,828
fairseq/examples/wav2vec/README.md,sha256=V-WoibcqXwNjeaO3tvZFMksDoM1emlRiwHVgI7EkgJ8,25830
fairseq/examples/wav2vec/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
fairseq/examples/wav2vec/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/wav2vec/__pycache__/libri_labels.cpython-311.pyc,,
fairseq/examples/wav2vec/__pycache__/vq-wav2vec_featurize.cpython-311.pyc,,
fairseq/examples/wav2vec/__pycache__/wav2vec_featurize.cpython-311.pyc,,
fairseq/examples/wav2vec/__pycache__/wav2vec_manifest.cpython-311.pyc,,
fairseq/examples/wav2vec/config/finetuning/base_100h.yaml,sha256=ndJKiuzU16IQD9j_bCcdGWB30xw7PRYD7e9Uq2mr4YU,951
fairseq/examples/wav2vec/config/finetuning/base_10h.yaml,sha256=pivnSagARAvFfX-J7aDcKqcN2zw4aUa7UlWwn1AIp-M,1090
fairseq/examples/wav2vec/config/finetuning/base_10m.yaml,sha256=fCDofh6wV8gADaE00bjJHwfdMabm4xGkJwHIDyYAeNA,1091
fairseq/examples/wav2vec/config/finetuning/base_1h.yaml,sha256=P3UD1jxpKSSVZA0cy1W4Xo_4BZwYSdHDMnfysMjSFLk,1091
fairseq/examples/wav2vec/config/finetuning/base_960h.yaml,sha256=hH2XwFZTXRAGryAfwk92aw-Kf2BtpVobczzjm46dduo,931
fairseq/examples/wav2vec/config/finetuning/vox_100h.yaml,sha256=ZaKpYEPU16PCeMnlBOmDPRePbniHN6hrvprwmRF9QIY,953
fairseq/examples/wav2vec/config/finetuning/vox_10h.yaml,sha256=D-JEq3S6hLuqMIEMkGrGSm4OK4ZPGMgvM_x1bqyDeLo,1088
fairseq/examples/wav2vec/config/finetuning/vox_10m.yaml,sha256=8K0BTexmzeZPh6W2jK_tywFeG2XZAj2vuJff8J1amTw,1089
fairseq/examples/wav2vec/config/finetuning/vox_1h.yaml,sha256=-tJXpChCmi1qcVhSjHLcjMARzDU9k3E7-zvVWIk9MgM,1089
fairseq/examples/wav2vec/config/finetuning/vox_960h.yaml,sha256=NVBeCkJEvR8xWyPs00ICCriLR5hdVy71jmU0l7aSrX0,937
fairseq/examples/wav2vec/config/pretraining/wav2vec2_base_librispeech.yaml,sha256=BD3JSj9OGFs5D8nj74PFP6kI7mm9LjuVGd6KNmY0vkk,990
fairseq/examples/wav2vec/config/pretraining/wav2vec2_conformer_base_librispeech.yaml,sha256=BSmKuHGE8C-C5J88zyGiolL_6N9wpOvDHg3ngqfSwOk,1058
fairseq/examples/wav2vec/config/pretraining/wav2vec2_conformer_large_librivox.yaml,sha256=n77t3xN4fcHAOIExjHw43SpvSq5s56I9PT_rVdyeOZk,1288
fairseq/examples/wav2vec/config/pretraining/wav2vec2_large_librivox.yaml,sha256=znXhU68n5uWgWEVn76GBGUOpEMPWPhU2SCdyho4WANQ,1236
fairseq/examples/wav2vec/config/pretraining/wav2vec2_large_librivox_tpu-pod.yaml,sha256=ia1bOaeLYa7RTqBSyHoldfbtCvJW2kahEu8dLQMLOoY,1309
fairseq/examples/wav2vec/config/pretraining/wav2vec2_large_librivox_tpu.yaml,sha256=Jz7zpHVv8gQQGEfn445FdfvNOP6WshMvZy4kvwWcKZ0,1432
fairseq/examples/wav2vec/libri_labels.py,sha256=w8cKKgWZZiu_30UjESvaYghF8T87aRnDcjat2MzFQxI,1875
fairseq/examples/wav2vec/scripts/binarize_manifest.sh,sha256=cmhcPvZ9IxZpZuxpyquJq7jRAy2XtjAMUi-iKAwmEwE,1105
fairseq/examples/wav2vec/unsupervised/README.md,sha256=61IidpakT4qgGXUgWtoN7Rs31dJG6t_1ZwQpkVw35ow,7834
fairseq/examples/wav2vec/unsupervised/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
fairseq/examples/wav2vec/unsupervised/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/wav2vec/unsupervised/__pycache__/w2vu_generate.cpython-311.pyc,,
fairseq/examples/wav2vec/unsupervised/config/finetuning/w2v_finetune.yaml,sha256=WuQE1R3MiZBAWH1JStZjBrHbI49zEV4nGQWDTla1DfM,1047
fairseq/examples/wav2vec/unsupervised/config/gan/w2vu.yaml,sha256=_AbNmjDJaRyyoGqyH-rZjH5tT9KesdfMbs4SEz-QvyI,2182
fairseq/examples/wav2vec/unsupervised/config/gan/w2vu2.yaml,sha256=xYUAEE2L7qLon0eJeW6frjtkF-BnaLu0RQQk1BTMwqY,3176
fairseq/examples/wav2vec/unsupervised/config/generate/viterbi.yaml,sha256=ge0lcn80tIRhcZx6-5WBShTqChSgU6qdeMBhBQgu0u0,303
fairseq/examples/wav2vec/unsupervised/config/timit_matched/test.uid,sha256=kjBPqae2w0WKI28FHX4FW-FadyJevHGkc9M5ZG_UKJM,2321
fairseq/examples/wav2vec/unsupervised/config/timit_matched/train.uid,sha256=TXPPDm0pi_uWNkKEPA3xHFcHfPzk7JZUD-RxioPBDbw,44833
fairseq/examples/wav2vec/unsupervised/config/timit_matched/train_text.uid,sha256=TXPPDm0pi_uWNkKEPA3xHFcHfPzk7JZUD-RxioPBDbw,44833
fairseq/examples/wav2vec/unsupervised/config/timit_matched/valid.uid,sha256=eWf8H1SJY8XLW9BEGTslF2ZNM2MSDKoi9_vLVoxetwE,4839
fairseq/examples/wav2vec/unsupervised/config/timit_unmatched/test.uid,sha256=gTkOmBPHt0PmRl3i9v2bRm1a-RW-LKoAitqWkVZ5YX8,19622
fairseq/examples/wav2vec/unsupervised/config/timit_unmatched/train.uid,sha256=ngEZLUy9OxCW9yuC__z5bWDrC9UxTR0GHBW5ZyD0Oec,34958
fairseq/examples/wav2vec/unsupervised/config/timit_unmatched/train_text.uid,sha256=qOil6Y3TdZq38nSrcnJItD6AItwpVf2ZKQSD4OnxcBc,11543
fairseq/examples/wav2vec/unsupervised/config/timit_unmatched/valid.uid,sha256=193pjUpdAdU3tjRhbi3P1_O2jI34ZtEE9v6sFXZGAws,7572
fairseq/examples/wav2vec/unsupervised/data/__init__.py,sha256=WuBtOMgFaiNBvkjQ8130tPalT-64ogGDxnqfH7F4mg4,370
fairseq/examples/wav2vec/unsupervised/data/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/wav2vec/unsupervised/data/__pycache__/extracted_features_dataset.cpython-311.pyc,,
fairseq/examples/wav2vec/unsupervised/data/__pycache__/random_input_dataset.cpython-311.pyc,,
fairseq/examples/wav2vec/unsupervised/data/extracted_features_dataset.py,sha256=L73ajKgHn-tG6YX4gUp7ftDV8Xn-Dj2QnGSR718TExc,5038
fairseq/examples/wav2vec/unsupervised/data/random_input_dataset.py,sha256=Hwi7ewBHUfjJ4hXVjQQIOHcB995kioe7XgR_pVwzv6s,1905
fairseq/examples/wav2vec/unsupervised/kaldi_self_train/README.md,sha256=CjBQBmfdarJ3rRp58UtF5V24uqdVmicUzypP4PE_4MM,2700
fairseq/examples/wav2vec/unsupervised/kaldi_self_train/st/cmd.sh,sha256=pwz49AZ2Bg4sVYJqXH7WBokwxdYSL6-pVi9MZ7lTJ3k,898
fairseq/examples/wav2vec/unsupervised/kaldi_self_train/st/decode_phone.sh,sha256=NZIrzK1sG5mHyxFPS1J3giX8T2qd388SqhNSS1G8Rio,1015
fairseq/examples/wav2vec/unsupervised/kaldi_self_train/st/decode_word_step1.sh,sha256=dPxYB6xQp5Su1P6VsB-RI_uhZKI6Ffv3gi2mQ3Ez__U,1592
fairseq/examples/wav2vec/unsupervised/kaldi_self_train/st/decode_word_step2.sh,sha256=htuPoVkHqHHShc0TGOKAB2VvBacL-JQvPRYm-w23CbM,886
fairseq/examples/wav2vec/unsupervised/kaldi_self_train/st/local/__pycache__/copy_aligned_text.cpython-311.pyc,,
fairseq/examples/wav2vec/unsupervised/kaldi_self_train/st/local/__pycache__/prepare_data_from_w2v.cpython-311.pyc,,
fairseq/examples/wav2vec/unsupervised/kaldi_self_train/st/local/__pycache__/unsup_select.cpython-311.pyc,,
fairseq/examples/wav2vec/unsupervised/kaldi_self_train/st/local/copy_aligned_text.py,sha256=hvNNK9gJVub-Wu8npu_U7ALQMgBZDOmSnSX5cA4UQSE,93
fairseq/examples/wav2vec/unsupervised/kaldi_self_train/st/local/decode.sh,sha256=ggeN4PF4t6Vr_3xx4MVZRe_ChpJQrwDiDJEoD3tLQTM,631
fairseq/examples/wav2vec/unsupervised/kaldi_self_train/st/local/prepare_data_from_w2v.py,sha256=1iYE0OYkWKHSu5hTU4X_VASZ8tsOpo0uYztlk8Uc3eY,2137
fairseq/examples/wav2vec/unsupervised/kaldi_self_train/st/local/prepare_lang.sh,sha256=RsVaAICIH8vxL4xH7KGgJhp70MtJYNyFR9xDzCSIOmw,890
fairseq/examples/wav2vec/unsupervised/kaldi_self_train/st/local/prepare_lang_word.sh,sha256=uIMRVUjQZ_yhqrqCiHD_27_eECaWWvs6bX_T-waQJvY,826
fairseq/examples/wav2vec/unsupervised/kaldi_self_train/st/local/prepare_lm.sh,sha256=EDjKrpGlv4ZbYB6tJX-Bp6RwDMthnCayrtgltRMTFh4,690
fairseq/examples/wav2vec/unsupervised/kaldi_self_train/st/local/score.sh,sha256=ENgB_FfLxXwZoycuuF9IlwuTVBAFuCxQCl73lfOjZ9c,2108
fairseq/examples/wav2vec/unsupervised/kaldi_self_train/st/local/show_wer.sh,sha256=zuO93V6LfJnHjhufjPCogNMJ7pmQoD2uDjZ_PoEUxIQ,1445
fairseq/examples/wav2vec/unsupervised/kaldi_self_train/st/local/train_subset_lgbeam.sh,sha256=hqBJnTZ84e5NK7OlBq93xFkRxr9TnTDT9WDsurluL8A,3807
fairseq/examples/wav2vec/unsupervised/kaldi_self_train/st/local/unsup_select.py,sha256=kv3ou-6If95RulQ3RYOVva81IQxZrfBXad7jAkBNfO0,4767
fairseq/examples/wav2vec/unsupervised/kaldi_self_train/st/local/unsup_select_decode.sh,sha256=JLsiFuejMc0HpCXFxceYDbVT-tSt8CsfalG2Jq7pX70,956
fairseq/examples/wav2vec/unsupervised/kaldi_self_train/st/local/unsup_select_decode_word.sh,sha256=3u5EDX0SUEbG7wgREWaHSotU34j6IsfIDHSzgkzunBA,984
fairseq/examples/wav2vec/unsupervised/kaldi_self_train/st/path.sh,sha256=kUqN6NtC-Wx26IXLbVUzjVyc-oW8Z9J8t5Y2uC-w7Z4,311
fairseq/examples/wav2vec/unsupervised/kaldi_self_train/st/steps_gan/train_deltas.sh,sha256=X9SjGDGoTf_hrXbaNZP5oJYVtNOSl6UydGaEfUUv6ZU,6803
fairseq/examples/wav2vec/unsupervised/kaldi_self_train/st/steps_gan/train_lda_mllt.sh,sha256=FBWw01JU77f47Wwp3c-t7TaWNnwdnTpNR35nzvTOntk,9363
fairseq/examples/wav2vec/unsupervised/kaldi_self_train/st/steps_gan/train_sat.sh,sha256=yhpXAxZstUvXQyhkQigXf09CUlqGIFyDNAXKhQyO0r8,11595
fairseq/examples/wav2vec/unsupervised/kaldi_self_train/st/train.sh,sha256=8LkIksIAuSiLx74HAw5A5_9ZBs4NxLa0_IWe3wXOvg4,1482
fairseq/examples/wav2vec/unsupervised/models/__init__.py,sha256=oTWOepSV4TbosrtTBNfLkkHXeVAqyXe_v8byMI7QyGw,244
fairseq/examples/wav2vec/unsupervised/models/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/wav2vec/unsupervised/models/__pycache__/wav2vec_u.cpython-311.pyc,,
fairseq/examples/wav2vec/unsupervised/models/wav2vec_u.py,sha256=q7ek0MCSfHzAAvz7xEi9Q13G4tTWrUOxuQM15gzuz88,22945
fairseq/examples/wav2vec/unsupervised/scripts/__pycache__/apply_pca.cpython-311.pyc,,
fairseq/examples/wav2vec/unsupervised/scripts/__pycache__/copy_labels.cpython-311.pyc,,
fairseq/examples/wav2vec/unsupervised/scripts/__pycache__/filter_lexicon.cpython-311.pyc,,
fairseq/examples/wav2vec/unsupervised/scripts/__pycache__/filter_tsv.cpython-311.pyc,,
fairseq/examples/wav2vec/unsupervised/scripts/__pycache__/g2p_wrd_to_phn.cpython-311.pyc,,
fairseq/examples/wav2vec/unsupervised/scripts/__pycache__/ltr_to_wrd.cpython-311.pyc,,
fairseq/examples/wav2vec/unsupervised/scripts/__pycache__/mean_pool.cpython-311.pyc,,
fairseq/examples/wav2vec/unsupervised/scripts/__pycache__/merge_clusters.cpython-311.pyc,,
fairseq/examples/wav2vec/unsupervised/scripts/__pycache__/normalize_and_filter_text.cpython-311.pyc,,
fairseq/examples/wav2vec/unsupervised/scripts/__pycache__/normalize_text.cpython-311.pyc,,
fairseq/examples/wav2vec/unsupervised/scripts/__pycache__/pca.cpython-311.pyc,,
fairseq/examples/wav2vec/unsupervised/scripts/__pycache__/phonemize_with_sil.cpython-311.pyc,,
fairseq/examples/wav2vec/unsupervised/scripts/__pycache__/remove_silence.cpython-311.pyc,,
fairseq/examples/wav2vec/unsupervised/scripts/__pycache__/vads.cpython-311.pyc,,
fairseq/examples/wav2vec/unsupervised/scripts/__pycache__/wav2vec_apply_cluster_faiss.cpython-311.pyc,,
fairseq/examples/wav2vec/unsupervised/scripts/__pycache__/wav2vec_cluster_faiss.cpython-311.pyc,,
fairseq/examples/wav2vec/unsupervised/scripts/__pycache__/wav2vec_extract_features.cpython-311.pyc,,
fairseq/examples/wav2vec/unsupervised/scripts/__pycache__/wer.cpython-311.pyc,,
fairseq/examples/wav2vec/unsupervised/scripts/__pycache__/wrd_to_ltr.cpython-311.pyc,,
fairseq/examples/wav2vec/unsupervised/scripts/apply_pca.py,sha256=XCJhOUT40-nKc7UJy02yinWpD7JrshEH00dEJOZN3VY,2496
fairseq/examples/wav2vec/unsupervised/scripts/copy_labels.py,sha256=QkqG2uHnRb-b51yFW4Fj4w5SijMgC7p9-D2pykAiBAQ,298
fairseq/examples/wav2vec/unsupervised/scripts/filter_lexicon.py,sha256=fSH0Ot721qUtvoMv0NIvzgUr2D0drPXBPU69aFdmcN0,939
fairseq/examples/wav2vec/unsupervised/scripts/filter_tsv.py,sha256=Fp75uaKJaJnWqoDp62Gz7jyM9xnEL-nd1Ntaj1l9Nm8,955
fairseq/examples/wav2vec/unsupervised/scripts/g2p_wrd_to_phn.py,sha256=DIzC2keJ6PymvL7N0vuzTJIqOC80qHPxWUq4DWHQcoo,1104
fairseq/examples/wav2vec/unsupervised/scripts/ltr_to_wrd.py,sha256=HbyUwGFVq4cxNEEjw2CGsFbCCh32C_iavjeQV4A6R3U,359
fairseq/examples/wav2vec/unsupervised/scripts/mean_pool.py,sha256=Zd2HwGt2gqdrEO861j_Zhrbz3Oo2X28zA7DV9Kh917U,3187
fairseq/examples/wav2vec/unsupervised/scripts/merge_clusters.py,sha256=JZRarKWE5bD0ZsUWLBA4_5I6bAtP1h9P8uPxJXZHR4k,3543
fairseq/examples/wav2vec/unsupervised/scripts/normalize_and_filter_text.py,sha256=eRlW1WAHHu-_eVqRinocLIqS2pQ10opdoxjoaeIx11Y,1997
fairseq/examples/wav2vec/unsupervised/scripts/normalize_text.py,sha256=ICJHABQwpW6phZHvKTqTiSEJWYSNKXjhJs2uRE_Okzg,489
fairseq/examples/wav2vec/unsupervised/scripts/pca.py,sha256=6rDHOgmD16RC_HF3X_QpgoqjCCTGl2thhblNV3j7a50,1471
fairseq/examples/wav2vec/unsupervised/scripts/phonemize_with_sil.py,sha256=-KqMpsZdrgdYfKOLfOt4lQ29sb3hpcrl7Po6eV3Uuk4,2045
fairseq/examples/wav2vec/unsupervised/scripts/prepare_audio.sh,sha256=mhp8HD22WiXIa4Mgpj1iYrEG8I2-OIX84Y6OXXMJIXk,2237
fairseq/examples/wav2vec/unsupervised/scripts/prepare_audio_v2.sh,sha256=_RjnW1XPAaB70ridRzMqZENqvfbCPJNhOdfpOabIt9M,1564
fairseq/examples/wav2vec/unsupervised/scripts/prepare_text.sh,sha256=7II7rui5FqDUjOXhjph0o6wvAKxhPvcQr-AxxPhUoAE,4668
fairseq/examples/wav2vec/unsupervised/scripts/prepare_timit.sh,sha256=6J2UZA0bWFKXmE629zIpAxGubYYX8PSEFICVpZJkCjs,3466
fairseq/examples/wav2vec/unsupervised/scripts/remove_silence.py,sha256=A3e8XSRIcyCwbA-HjlBfUhGiWU_FopQqgx21QC6xxO8,1927
fairseq/examples/wav2vec/unsupervised/scripts/vads.py,sha256=pwk7Uyh_Kk-YxHLpcYWPe9VMaMnjvzqXMGvKfslFUF0,2569
fairseq/examples/wav2vec/unsupervised/scripts/wav2vec_apply_cluster_faiss.py,sha256=OmLhQkT1RyaGLR2L4kon4RMv5ysS3-cJulMP61iPdSo,4015
fairseq/examples/wav2vec/unsupervised/scripts/wav2vec_cluster_faiss.py,sha256=x9ZyugXCN0XvOq9FRkH9K_gq1HLscprtB4wGJx-ksQM,6315
fairseq/examples/wav2vec/unsupervised/scripts/wav2vec_extract_features.py,sha256=PqbngNmG3jN8DEKnxsBW6k-wpVhOy8J4AmOvu85kva4,3673
fairseq/examples/wav2vec/unsupervised/scripts/wer.py,sha256=0NA42BgZ_scUFL_f4FM6CyV7QnUHiJ58QEmjH26PYMk,2264
fairseq/examples/wav2vec/unsupervised/scripts/wrd_to_ltr.py,sha256=rrHxxeMYQyGQfaznRkRGf9rqK2I3kcEqf9Mjs471Ipc,365
fairseq/examples/wav2vec/unsupervised/tasks/__init__.py,sha256=piuMGOu2Wi2n85I1xJYDgbyhy0nssNUj-4Qdnsu6W7U,270
fairseq/examples/wav2vec/unsupervised/tasks/__pycache__/__init__.cpython-311.pyc,,
fairseq/examples/wav2vec/unsupervised/tasks/__pycache__/unpaired_audio_text.cpython-311.pyc,,
fairseq/examples/wav2vec/unsupervised/tasks/unpaired_audio_text.py,sha256=RX9jathdO1wJefa4173fu0vwtq5QKgnLx4H3RsulUk8,15658
fairseq/examples/wav2vec/unsupervised/w2vu_generate.py,sha256=rC0y-yoyUYay-faWErvlJalPjDoKF3d-LocE78CkmGM,22454
fairseq/examples/wav2vec/vq-wav2vec_featurize.py,sha256=5FZuSapXO4bzh0zqOXpvaYZm_F8GIr0YWlyG9ZYJp0Q,7680
fairseq/examples/wav2vec/wav2vec_featurize.py,sha256=D_Hj2roZPC0smWKvidzgOv91Cd521xgWxYI3Zu-KzOk,7020
fairseq/examples/wav2vec/wav2vec_manifest.py,sha256=301xfaSmhmv44eFL6mpSDjnrsNxreNX6Th3IH9Kfsvk,2513
fairseq/examples/wav2vec/xlsr/README.md,sha256=7FQ0YmQzMh9KDt0y0z3_p3p4QC2kndgUHJJ2Ge7hzmA,4419
fairseq/examples/wav2vec/xlsr/config/finetune.yaml,sha256=I_WrJ9QpYhenLKrw0TBu1ikfgeSO2aglJaS2DVXsOio,1151
fairseq/examples/wmt19/README.md,sha256=4yNodSa2aiKjB8KSL3W2Eg9VIxksvsKRWRgjP4IAI6E,4646
fairseq/examples/wmt20/README.md,sha256=55QHIs9GVMBe7D18nUUJ2T10VHB75CqZ2iTxIeKEOd8,4150
fairseq/examples/wmt21/README.md,sha256=tYhKhmVZAFQIE8H4VzBZVnWqQi1Eoo2ZhuRnTuX9TD0,821
fairseq/examples/wmt21/eval.sh,sha256=ULx1cu8Kq0K97MR0PmpT9VPoCotGnDAj7dM1EUMZy98,1836
fairseq/examples/wmt21/scripts/normalize-punctuation.perl,sha256=TTwtutzgSubk1SzXlbmzmvtnfRLc5OULlosDfd3l704,1895
fairseq/examples/wmt21/scripts/replace-unicode-punctuation.perl,sha256=Kkv5tYpm_ViTHOVLibbpkS4xd1Ura_7fPXzoqJqqcd4,872
fairseq/examples/womens_bios/README.md,sha256=dAkI3Ogn-y0v7A5a_5uIf4UJ9SDe9LEv_rUTjfj4l1s,5722
fairseq/examples/womens_bios/__pycache__/query_occupations_from_wikidata.cpython-311.pyc,,
fairseq/examples/womens_bios/query_occupations_from_wikidata.py,sha256=pX1L8NLodDYxAxBD-QzFo0JwGRPupbExwds_alQEEBA,1201
fairseq/examples/xformers/README.md,sha256=WHH2cWb1OFq1kL9oF4oU-f4juCZfmPjU7OFJtpzlcQM,2046
fairseq/examples/xglm/README.md,sha256=Vsejv1i_5TiTE3DThCMijOxwHjd7z4v_ALL4J8nztGM,8257
fairseq/examples/xglm/model_card.md,sha256=rhViwSVlD0tv0hmy4BiKZt6eubVKFX1TpRpV-Pnauzs,10746
fairseq/examples/xlmr/README.md,sha256=SE_VnzfA_QZ-ioYvDx6QXVCLapwiHoa43duxMtmDedk,6560
fairseq/examples/xmod/README.md,sha256=Yf_7xfWsXyw5a8PzBuBjiHReqnIpisV-yFc29GsFtYQ,5730
fairseq/examples/xmod/__pycache__/preprocess_nli.cpython-311.pyc,,
fairseq/examples/xmod/preprocess_nli.py,sha256=opEoW2QnmpD0ykeT3I5jsNRdJT9ljztU2Aq8uX-ARio,6168
fairseq/file_chunker_utils.py,sha256=2WhJIqiE9_QKlP5YOeIaz5vxXAfGHISDhrYualvatA0,2691
fairseq/file_io.py,sha256=tfyks-lr9n-DELdveBt6iwseSWlLDGVUWRTaa1JJnVg,5614
fairseq/file_utils.py,sha256=ioIVO6jY1djgGFXh2KH3cu2BRenzR6eAhco_RJXmHcs,11718
fairseq/hub_utils.py,sha256=FjLtrVwBKOn2xXU1gnIxHizZtTA5tjgxRAnqVUnS2Vg,11350
fairseq/incremental_decoding_utils.py,sha256=xLQ5KkzPIY7g76ny-ch4YGxkWfa0iSNer0c2cjGJDeM,1773
fairseq/iterative_refinement_generator.py,sha256=Y9EPmHaUTkUEstRuhOETF9Pej-3kYP4_tgBAQPeuqm4,13238
fairseq/libbleu.cp311-win_amd64.pyd,sha256=9td7OtNHdiyNuCFxTiFQo3xuAnfRKcpUdH8FrXlJeVM,15872
fairseq/logging/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
fairseq/logging/__pycache__/__init__.cpython-311.pyc,,
fairseq/logging/__pycache__/meters.cpython-311.pyc,,
fairseq/logging/__pycache__/metrics.cpython-311.pyc,,
fairseq/logging/__pycache__/progress_bar.cpython-311.pyc,,
fairseq/logging/meters.py,sha256=MEHFGx1tFM_GmvielBIi3-ofxRUFSoFPw2NIiWXk6uU,8710
fairseq/logging/metrics.py,sha256=9--3xny2ldhiUkx1E-zNZ-UFVFQ4Wb4ODSujrC52K-I,10012
fairseq/logging/progress_bar.py,sha256=ZoyatGMbh0QruT6eT_judRYlhcCbFqOrLgbbA-AbAog,18173
fairseq/model_parallel/__init__.py,sha256=ABekRsj8vHE4lup-dGFKfZLLlFQFiKHs9_q7TV3c564,228
fairseq/model_parallel/__pycache__/__init__.cpython-311.pyc,,
fairseq/model_parallel/__pycache__/megatron_trainer.cpython-311.pyc,,
fairseq/model_parallel/criterions/__init__.py,sha256=wkU7nQQDhIJitiWVEaVjxl10i0TONzmCgdXCZqh0r8o,514
fairseq/model_parallel/criterions/__pycache__/__init__.cpython-311.pyc,,
fairseq/model_parallel/criterions/__pycache__/vocab_parallel_cross_entropy.cpython-311.pyc,,
fairseq/model_parallel/criterions/vocab_parallel_cross_entropy.py,sha256=-GyAHCVU-ZpfsmWpIsaYyxSoUhMPUmFz_RdqvKiFzRM,3157
fairseq/model_parallel/megatron_trainer.py,sha256=O0Ydmx87FBMWTRzKEfk5k7qkXFI34oG97A7aBO8Bmq4,2574
fairseq/model_parallel/models/__init__.py,sha256=rE-4VUwN8iZw-o48QDpAogEJu1pJ88KmQLXyDQKJyx4,701
fairseq/model_parallel/models/__pycache__/__init__.cpython-311.pyc,,
fairseq/model_parallel/models/__pycache__/transformer.cpython-311.pyc,,
fairseq/model_parallel/models/__pycache__/transformer_lm.cpython-311.pyc,,
fairseq/model_parallel/models/pipeline_parallel_transformer/__init__.py,sha256=ZDUFYI3lb4AIVEZ0fbj9JRwOaqh3dWHuvx82xJ5GVek,207
fairseq/model_parallel/models/pipeline_parallel_transformer/__pycache__/__init__.cpython-311.pyc,,
fairseq/model_parallel/models/pipeline_parallel_transformer/__pycache__/layers.cpython-311.pyc,,
fairseq/model_parallel/models/pipeline_parallel_transformer/__pycache__/model.cpython-311.pyc,,
fairseq/model_parallel/models/pipeline_parallel_transformer/layers.py,sha256=hczBmgaldAK1UvuSIJZ0ZtxmntZWANw40s3J72DYtU8,22687
fairseq/model_parallel/models/pipeline_parallel_transformer/model.py,sha256=Df9oHmuUpVhpBMmTx8uNPu7Cp0yU3UcMFUqPpDt_Zv8,33889
fairseq/model_parallel/models/roberta/__init__.py,sha256=ZDUFYI3lb4AIVEZ0fbj9JRwOaqh3dWHuvx82xJ5GVek,207
fairseq/model_parallel/models/roberta/__pycache__/__init__.cpython-311.pyc,,
fairseq/model_parallel/models/roberta/__pycache__/model.cpython-311.pyc,,
fairseq/model_parallel/models/roberta/model.py,sha256=gcuEEg_vUwRO4_KH8lSbuEDWDOsXFq3m4obu0F9BT6Y,8003
fairseq/model_parallel/models/transformer.py,sha256=IcQkFqdwcnxsmwFUwJRaBA3hjiME1xAcwx9W-QxZHvg,3905
fairseq/model_parallel/models/transformer_lm.py,sha256=NSOuCKO0csmlwCpn5c29Q3_yenNWscN90WVIOU_NURQ,7581
fairseq/model_parallel/modules/__init__.py,sha256=wyYx42MYdgQsDItTTdTQfCG6NcqtoUJhtILArGI30as,526
fairseq/model_parallel/modules/__pycache__/__init__.cpython-311.pyc,,
fairseq/model_parallel/modules/__pycache__/multihead_attention.cpython-311.pyc,,
fairseq/model_parallel/modules/__pycache__/transformer_layer.cpython-311.pyc,,
fairseq/model_parallel/modules/multihead_attention.py,sha256=2NG6QBFRfMvWosYUuF8nU_JmDAeWO5sZjDrVeInZFrA,13302
fairseq/model_parallel/modules/transformer_layer.py,sha256=yWg_urpF0hAYW9mMdpN-al8dpYIeTB2MpTklsHAdii8,2847
fairseq/models/__init__.py,sha256=c8Se7b2e1PCTi9W4bsvKOV7aVgAdaos1qJXN5ejwc88,8247
fairseq/models/__pycache__/__init__.cpython-311.pyc,,
fairseq/models/__pycache__/composite_encoder.cpython-311.pyc,,
fairseq/models/__pycache__/distributed_fairseq_model.cpython-311.pyc,,
fairseq/models/__pycache__/fairseq_decoder.cpython-311.pyc,,
fairseq/models/__pycache__/fairseq_encoder.cpython-311.pyc,,
fairseq/models/__pycache__/fairseq_incremental_decoder.cpython-311.pyc,,
fairseq/models/__pycache__/fairseq_model.cpython-311.pyc,,
fairseq/models/__pycache__/fconv.cpython-311.pyc,,
fairseq/models/__pycache__/fconv_lm.cpython-311.pyc,,
fairseq/models/__pycache__/fconv_self_att.cpython-311.pyc,,
fairseq/models/__pycache__/lightconv.cpython-311.pyc,,
fairseq/models/__pycache__/lightconv_lm.cpython-311.pyc,,
fairseq/models/__pycache__/lstm.cpython-311.pyc,,
fairseq/models/__pycache__/lstm_lm.cpython-311.pyc,,
fairseq/models/__pycache__/masked_lm.cpython-311.pyc,,
fairseq/models/__pycache__/model_utils.cpython-311.pyc,,
fairseq/models/__pycache__/multilingual_transformer.cpython-311.pyc,,
fairseq/models/__pycache__/transformer_align.cpython-311.pyc,,
fairseq/models/__pycache__/transformer_from_pretrained_xlm.cpython-311.pyc,,
fairseq/models/__pycache__/transformer_lm.cpython-311.pyc,,
fairseq/models/__pycache__/transformer_ulm.cpython-311.pyc,,
fairseq/models/bart/__init__.py,sha256=_eb3040QbNhPT8IKLL0QtvhZx0jbHq_EKAvx-fZYI4E,244
fairseq/models/bart/__pycache__/__init__.cpython-311.pyc,,
fairseq/models/bart/__pycache__/hub_interface.cpython-311.pyc,,
fairseq/models/bart/__pycache__/model.cpython-311.pyc,,
fairseq/models/bart/hub_interface.py,sha256=s8-cLFW114HCKh2PqDV087M9WGVFCuPnSwpZxqBCUMA,7863
fairseq/models/bart/model.py,sha256=MiId0MczYQWjvAJ1D1ZRyvDw-0FVKZbtlywgYTM6_As,15998
fairseq/models/composite_encoder.py,sha256=HZGqK5ApxNAz6TTZYjradhqFTU15XOj-3MszBfc6V_w,1928
fairseq/models/distributed_fairseq_model.py,sha256=8q_hqN6UuV40USSVVeKkDhZNNsr6a7QLLh03wSVV7G4,5740
fairseq/models/ema/__init__.py,sha256=8gcg6nmBG7i3UbuU2RY0yJgR9lz5tte8tqP5fepA5Yk,599
fairseq/models/ema/__pycache__/__init__.cpython-311.pyc,,
fairseq/models/ema/__pycache__/ema.cpython-311.pyc,,
fairseq/models/ema/ema.py,sha256=-YQ1Y6WsVYzDzeCZMHD-EwrfdQvacKpUzX2xcPYWBPQ,8135
fairseq/models/fairseq_decoder.py,sha256=lHAuG3D17GPSnpAJjqz4Z_1fmhSNLZOdQbyqYPwDL40,3750
fairseq/models/fairseq_encoder.py,sha256=JMoG0Fvqp7p9DCygw5ZmsSUpDU3E8aCWByBLyyGPTLw,2930
fairseq/models/fairseq_incremental_decoder.py,sha256=vjRSWzGI0mVe0r4usTeawN3C764H3b7npqC96qiNUg8,4468
fairseq/models/fairseq_model.py,sha256=iB6LYzSai_JuSR4XJLGTS64nTou-Wuld4gO43nanS90,20507
fairseq/models/fconv.py,sha256=QyWX3vN1qtIisrqdzbW1GwpGuZLpD4qOFkGwkcjT64A,28661
fairseq/models/fconv_lm.py,sha256=kHSqwOK5CKAB0VJrnrLeFor7Hq2ld4V4Fp8-ZcuUh3c,5015
fairseq/models/fconv_self_att.py,sha256=g0Y3Ofe6eY1zCYx7F6LG6hO2_fjzCDuld8vKIYZNejY,25347
fairseq/models/hubert/__init__.py,sha256=ksP2vI_O6l-F0mtFhOh9LyYYyj2v2p46EscLzEGj190,242
fairseq/models/hubert/__pycache__/__init__.cpython-311.pyc,,
fairseq/models/hubert/__pycache__/hubert.cpython-311.pyc,,
fairseq/models/hubert/__pycache__/hubert_asr.cpython-311.pyc,,
fairseq/models/hubert/hubert.py,sha256=aeLOw5Jkbi-K82R-p9Miq3smS2le0yrSOj0VxX9JJ8I,20003
fairseq/models/hubert/hubert_asr.py,sha256=KplYvGoIndwfLLIex8-Hdm-2YGby82aAay0GBMUZkgI,12488
fairseq/models/huggingface/__init__.py,sha256=cFnpynRxtpP5Jx93fNQRz7tdBbgeHljQk_-cqdOwRZk,710
fairseq/models/huggingface/__pycache__/__init__.cpython-311.pyc,,
fairseq/models/huggingface/__pycache__/hf_gpt2.cpython-311.pyc,,
fairseq/models/huggingface/hf_gpt2.py,sha256=BlPuQWF_q8Vuf8Re7gzEoCJhhcKTqj93kNxG6s3YvOs,5769
fairseq/models/lightconv.py,sha256=wxGdq--EK8V52cDC-Vs1KwAgwGTkSmafGJ3c3QDHbaY,38652
fairseq/models/lightconv_lm.py,sha256=q9TczaaaVoeQz5_mYJ6R_eVzz_j57dT4oIdD9Nab8E8,11154
fairseq/models/lstm.py,sha256=7dzkbfqjh_0jHhiFHW-pT5uX38OnNHPt67AEJ6hXQAY,30557
fairseq/models/lstm_lm.py,sha256=pJZpPUh5yBxCVzm3eHMhgzGDfkqbl_vRnxtpz_M7bJM,6423
fairseq/models/masked_lm.py,sha256=g4TUptCmkDtjpKnZemMWke3PCDfEnNrJLvt_xZtmjO8,15244
fairseq/models/model_utils.py,sha256=k3MX1pkRNaZaST-cZWvS7XhdboB9m6H1rtDpdG-vtVQ,2343
fairseq/models/multilingual_transformer.py,sha256=rhM5UdUNIs9ogdLnxY9KtwtrUb-rnKC9WKPPObSdax0,9570
fairseq/models/nat/__init__.py,sha256=6NIhjyPP1dwAKV2yO1-UtVGJHlshNtSlGKBwLSJkXl8,476
fairseq/models/nat/__pycache__/__init__.cpython-311.pyc,,
fairseq/models/nat/__pycache__/cmlm_transformer.cpython-311.pyc,,
fairseq/models/nat/__pycache__/fairseq_nat_model.cpython-311.pyc,,
fairseq/models/nat/__pycache__/insertion_transformer.cpython-311.pyc,,
fairseq/models/nat/__pycache__/iterative_nonautoregressive_transformer.cpython-311.pyc,,
fairseq/models/nat/__pycache__/levenshtein_transformer.cpython-311.pyc,,
fairseq/models/nat/__pycache__/levenshtein_utils.cpython-311.pyc,,
fairseq/models/nat/__pycache__/nat_crf_transformer.cpython-311.pyc,,
fairseq/models/nat/__pycache__/nonautoregressive_ensembles.cpython-311.pyc,,
fairseq/models/nat/__pycache__/nonautoregressive_transformer.cpython-311.pyc,,
fairseq/models/nat/cmlm_transformer.py,sha256=tU4ETsEIpx9GZ-aenW-qz9RGc00h5Y7LZYTeXHAxCKg,6453
fairseq/models/nat/fairseq_nat_model.py,sha256=PtbHlXuSALEZHRLY80gEVvunt5cYDEWB96wSeUL16yg,5555
fairseq/models/nat/insertion_transformer.py,sha256=2uIRSpA5bEtU9RbkMU77h1UTRXI68Xm3aS6T5ObuFsU,10460
fairseq/models/nat/iterative_nonautoregressive_transformer.py,sha256=rvrPnXqPEayllSn0lpxgJXaQfn96hwx2rASuk9RGi4Q,8647
fairseq/models/nat/levenshtein_transformer.py,sha256=Fb3KconzS1oIFSQ95XnGQ6HoMu1IW6EbdId4jdpT9WI,20131
fairseq/models/nat/levenshtein_utils.py,sha256=eSJKm54PsUyjk77b2GNt_dbJKDJWwP_ItgZiDRvts6M,9508
fairseq/models/nat/nat_crf_transformer.py,sha256=NU_u175ZCmbTVIFy51EjPopdbiFQYd3daxgk-hgBDcY,4378
fairseq/models/nat/nonautoregressive_ensembles.py,sha256=wyrKd-WJ9cN_Yp8j0ot3i7n9pvQ6-2p1JgqfLkB3Fyg,9289
fairseq/models/nat/nonautoregressive_transformer.py,sha256=Ar1WYNm-Y5aIBEaznl-MGKyvh20uDEj6e07BOeT5cf0,16891
fairseq/models/roberta/__init__.py,sha256=YMEHF3kZ6mugrTHKpYPyfAm1g-JsOmcn1a1hJ3w9vo8,386
fairseq/models/roberta/__pycache__/__init__.cpython-311.pyc,,
fairseq/models/roberta/__pycache__/alignment_utils.cpython-311.pyc,,
fairseq/models/roberta/__pycache__/enc_dec.cpython-311.pyc,,
fairseq/models/roberta/__pycache__/hub_interface.cpython-311.pyc,,
fairseq/models/roberta/__pycache__/model.cpython-311.pyc,,
fairseq/models/roberta/__pycache__/model_camembert.cpython-311.pyc,,
fairseq/models/roberta/__pycache__/model_gottbert.cpython-311.pyc,,
fairseq/models/roberta/__pycache__/model_xlmr.cpython-311.pyc,,
fairseq/models/roberta/alignment_utils.py,sha256=iqx4nnKkOSi9SpqBnprvgDEPcHkI_eQAyl4J_MKZWUI,4091
fairseq/models/roberta/enc_dec.py,sha256=kM8eEPIUNTUUq1Afc7Z6bZZalh_cYpGiHJtiIyF96d4,8076
fairseq/models/roberta/hub_interface.py,sha256=4mqTzlmp5Al4ivsPAcTfpvK-wW2cXMJrDmpct6F5NHg,8857
fairseq/models/roberta/model.py,sha256=569AkFxYZ8edCnuAPTmhkTa4hH9aFg9VpVWAA6KU568,26608
fairseq/models/roberta/model_camembert.py,sha256=VcPAcsocbJNFZcbyplA86udFdP18aXFMjXEqAs6tmtY,1942
fairseq/models/roberta/model_gottbert.py,sha256=80HYpelc6QiEj1qXtkuZbZKNWiQY6Bc6NCZ4FNocSPs,1376
fairseq/models/roberta/model_xlmr.py,sha256=m_kV2Ru-Cv5nFw9jSnAmnBo89INiUjd3wwwe0pijHx8,1442
fairseq/models/speech_to_speech/__init__.py,sha256=l11cYlTCX2UCAizGzwGgMwceQQE3vEmH-pmWd1lK_3A,285
fairseq/models/speech_to_speech/__pycache__/__init__.cpython-311.pyc,,
fairseq/models/speech_to_speech/__pycache__/modules.cpython-311.pyc,,
fairseq/models/speech_to_speech/__pycache__/s2s_conformer.cpython-311.pyc,,
fairseq/models/speech_to_speech/__pycache__/s2s_transformer.cpython-311.pyc,,
fairseq/models/speech_to_speech/modules.py,sha256=UZVzRS0rgUCY8-sKVwCgUiWGNK6wV0OyAgFKdrWKVW8,2214
fairseq/models/speech_to_speech/s2s_conformer.py,sha256=YfCH_pU8n2k86luzjl1mZZFVxV1ufQqefRIXsoYCvJo,4071
fairseq/models/speech_to_speech/s2s_transformer.py,sha256=2PB8_0L4pKC2Ts7qp_-U15_9JGU_6XkkvdfS3_MmZMo,25037
fairseq/models/speech_to_text/__init__.py,sha256=QsILhlPDcC_jV7uVpPqnsfQGH2Jj45OPJY-PMRG4eII,448
fairseq/models/speech_to_text/__pycache__/__init__.cpython-311.pyc,,
fairseq/models/speech_to_text/__pycache__/berard.cpython-311.pyc,,
fairseq/models/speech_to_text/__pycache__/convtransformer.cpython-311.pyc,,
fairseq/models/speech_to_text/__pycache__/hub_interface.cpython-311.pyc,,
fairseq/models/speech_to_text/__pycache__/multi_modality_model.cpython-311.pyc,,
fairseq/models/speech_to_text/__pycache__/s2t_conformer.cpython-311.pyc,,
fairseq/models/speech_to_text/__pycache__/s2t_transformer.cpython-311.pyc,,
fairseq/models/speech_to_text/__pycache__/s2t_wav_transformer.cpython-311.pyc,,
fairseq/models/speech_to_text/__pycache__/utils.cpython-311.pyc,,
fairseq/models/speech_to_text/__pycache__/xm_transformer.cpython-311.pyc,,
fairseq/models/speech_to_text/berard.py,sha256=cW9ZcvB90cRhVyxzyxR6Ghy1F2Fd3CzARIu07n_ATtU,23124
fairseq/models/speech_to_text/convtransformer.py,sha256=ilBPtnAIGseSotF6-y4EaOrHsLxkzC-ZrHj2zp3yBVo,16567
fairseq/models/speech_to_text/hub_interface.py,sha256=1qBL18aYkjABmxfs8CmrG3cHWFcQGr7bBw9Py03ACHg,4716
fairseq/models/speech_to_text/multi_modality_model.py,sha256=8UUiu9oe3jpHftwJ2ug4PwZeWgajwp6fZFPs5yAqqZE,1884
fairseq/models/speech_to_text/s2t_conformer.py,sha256=cl0Ae-oF0dVgESrLptN4hwikJ6yYPQKGMeu1LWFfbV8,6492
fairseq/models/speech_to_text/s2t_transformer.py,sha256=gMvbaOAL37wZ0ZAbaEe-NP9Yt5utje4q824V_2VsiB4,21049
fairseq/models/speech_to_text/s2t_wav_transformer.py,sha256=f576gJxCBhG4yv-dXKJGEm5VkPDJdMPiC2AalmKzpuc,17053
fairseq/models/speech_to_text/utils.py,sha256=gusJEEETxqyXNEhj8-7RljJMAWtqZhNhP9kyb8sCuQM,18585
fairseq/models/speech_to_text/xm_transformer.py,sha256=U7knKYJfoulRSGcRJco1OOc7mJ5gSAPHMvksLZli5Bc,26922
fairseq/models/text_to_speech/__init__.py,sha256=FK5ItSNU80V2sR9f11a470xHqdK98LF2RG9hBtCaSGo,285
fairseq/models/text_to_speech/__pycache__/__init__.cpython-311.pyc,,
fairseq/models/text_to_speech/__pycache__/codehifigan.cpython-311.pyc,,
fairseq/models/text_to_speech/__pycache__/fastspeech2.cpython-311.pyc,,
fairseq/models/text_to_speech/__pycache__/hifigan.cpython-311.pyc,,
fairseq/models/text_to_speech/__pycache__/hub_interface.cpython-311.pyc,,
fairseq/models/text_to_speech/__pycache__/tacotron2.cpython-311.pyc,,
fairseq/models/text_to_speech/__pycache__/tts_transformer.cpython-311.pyc,,
fairseq/models/text_to_speech/__pycache__/vocoder.cpython-311.pyc,,
fairseq/models/text_to_speech/codehifigan.py,sha256=F-w-WTguvHjCVpkbywDHyfvQCwKrX6MKqF43nC_bBRY,3637
fairseq/models/text_to_speech/fastspeech2.py,sha256=jaOxAEaD4C8wWqSeQEv0oftNf5Pmi2rqctUB_gvPhcM,15718
fairseq/models/text_to_speech/hifigan.py,sha256=4IZdzgjyIg3cyDZgq5wBLB75Cs993HjeDYwGTkTZuwc,5648
fairseq/models/text_to_speech/hub_interface.py,sha256=0HK7CUdGiHJa9JVIgd1BhotpXrLZCEGDvWh_gpC9l3o,4678
fairseq/models/text_to_speech/tacotron2.py,sha256=dz3k5t38zREUyxocOwbieBR-2L_5YsORkEXJ41tAlUM,15041
fairseq/models/text_to_speech/tts_transformer.py,sha256=CTn-PSbcsfGLf5D8bXIcxhseF5Wb8VXwU7tp3Lnqzmo,16971
fairseq/models/text_to_speech/vocoder.py,sha256=QzJxLZ__mYwTnX9R2aiTw-3_be95aY0sQwb8QqUqR78,9046
fairseq/models/transformer/__init__.py,sha256=bufdmYLmmKFCpxB6mR2dlAUu1ndW92_pcWuHJdZo-RU,1488
fairseq/models/transformer/__pycache__/__init__.cpython-311.pyc,,
fairseq/models/transformer/__pycache__/transformer_base.cpython-311.pyc,,
fairseq/models/transformer/__pycache__/transformer_config.cpython-311.pyc,,
fairseq/models/transformer/__pycache__/transformer_decoder.cpython-311.pyc,,
fairseq/models/transformer/__pycache__/transformer_encoder.cpython-311.pyc,,
fairseq/models/transformer/__pycache__/transformer_legacy.cpython-311.pyc,,
fairseq/models/transformer/transformer_base.py,sha256=5i6jjnaK2_Qa7zdiCu79fGRygeBh5ALoCbJxzl7VbUg,6485
fairseq/models/transformer/transformer_config.py,sha256=KoxuqwDDrC0DiWlw7xPGi_WwzkVNdW36BPchGyL8sL4,13371
fairseq/models/transformer/transformer_decoder.py,sha256=q_1M7iBSoeVX0CtyZ88WZhgDljgDO1RalhhQ4bhl8J8,17883
fairseq/models/transformer/transformer_encoder.py,sha256=Nk25o8xpJag7t-F9j2YcQxAqcFwYkxIIxHULOTC34zM,16909
fairseq/models/transformer/transformer_legacy.py,sha256=FdPPw8Iah5Kkanzp8Y6Ti-4zk5XAsAIV2xmQoWOiq8I,13586
fairseq/models/transformer_align.py,sha256=p2Qac-DhcF0SwdCCmau1cH_jCDC2EIDWrzVyNi5guuA,3532
fairseq/models/transformer_from_pretrained_xlm.py,sha256=jnWCJUjLr2DdvPzJVs__aPyiw4IPj4ocjv4BejNoeOg,6076
fairseq/models/transformer_lm.py,sha256=IfKmKWF0AEwfWJjdMx-p7P1SL08h-gpFFxsqmjsliyI,26790
fairseq/models/transformer_ulm.py,sha256=xXmxoRaiAbQLiXSicelDbyyCIgkoJph7_Xq0w6n0j5k,13870
fairseq/models/wav2vec/__init__.py,sha256=Gh0sH-ya-FtHK6JUmQg0uxx3YGvCNiNziSpOfkMoqQ4,277
fairseq/models/wav2vec/__pycache__/__init__.cpython-311.pyc,,
fairseq/models/wav2vec/__pycache__/utils.cpython-311.pyc,,
fairseq/models/wav2vec/__pycache__/wav2vec.cpython-311.pyc,,
fairseq/models/wav2vec/__pycache__/wav2vec2.cpython-311.pyc,,
fairseq/models/wav2vec/__pycache__/wav2vec2_asr.cpython-311.pyc,,
fairseq/models/wav2vec/utils.py,sha256=DHbuB1CdTl0vA9Wrcfv5tX2sP0N9GnmmVclLBxYUID8,680
fairseq/models/wav2vec/wav2vec.py,sha256=2Pi2VM7QbP6HFGmyfMci5xoL5KK3J_b0Zuz5_4I0BvQ,20928
fairseq/models/wav2vec/wav2vec2.py,sha256=qXlR_BxI_QL47S6zK3IVv8zhS4-Cox5ZOCwG8SUFuVk,43514
fairseq/models/wav2vec/wav2vec2_asr.py,sha256=6nm7lPpQUm1TMznQ4O9MniJ1mIZze8iB1VJabj56XTI,26724
fairseq/models/xmod/__init__.py,sha256=BouS8-NHkwsWSfgr1XFLpoP2vIWPO6kWvfZN1M8-GYw,253
fairseq/models/xmod/__pycache__/__init__.cpython-311.pyc,,
fairseq/models/xmod/__pycache__/hub_interface.cpython-311.pyc,,
fairseq/models/xmod/__pycache__/model.cpython-311.pyc,,
fairseq/models/xmod/__pycache__/transformer_layer_xmod.cpython-311.pyc,,
fairseq/models/xmod/hub_interface.py,sha256=li7802Cs-PlHjdG-g_9r9VqI4RPZ7EyOqoXxatkyM6A,1712
fairseq/models/xmod/model.py,sha256=85Axfn26lJYlxBkbTBPcT647Bio7uZhuSilloUp_-h0,22586
fairseq/models/xmod/transformer_layer_xmod.py,sha256=yPuqbflGFQyFyO957yhjqVuXr2kj1BFu_dmnsOkuHIk,7081
fairseq/modules/__init__.py,sha256=PuG7-NOUO28WxY71jK6s7rov6fIedB3QLun5TvTP78k,3602
fairseq/modules/__pycache__/__init__.cpython-311.pyc,,
fairseq/modules/__pycache__/adaptive_input.cpython-311.pyc,,
fairseq/modules/__pycache__/adaptive_softmax.cpython-311.pyc,,
fairseq/modules/__pycache__/base_layer.cpython-311.pyc,,
fairseq/modules/__pycache__/beamable_mm.cpython-311.pyc,,
fairseq/modules/__pycache__/character_token_embedder.cpython-311.pyc,,
fairseq/modules/__pycache__/checkpoint_activations.cpython-311.pyc,,
fairseq/modules/__pycache__/conformer_layer.cpython-311.pyc,,
fairseq/modules/__pycache__/conv_tbc.cpython-311.pyc,,
fairseq/modules/__pycache__/cross_entropy.cpython-311.pyc,,
fairseq/modules/__pycache__/downsampled_multihead_attention.cpython-311.pyc,,
fairseq/modules/__pycache__/dynamic_convolution.cpython-311.pyc,,
fairseq/modules/__pycache__/dynamic_crf_layer.cpython-311.pyc,,
fairseq/modules/__pycache__/ema_module.cpython-311.pyc,,
fairseq/modules/__pycache__/espnet_multihead_attention.cpython-311.pyc,,
fairseq/modules/__pycache__/fairseq_dropout.cpython-311.pyc,,
fairseq/modules/__pycache__/fp32_batch_norm.cpython-311.pyc,,
fairseq/modules/__pycache__/fp32_group_norm.cpython-311.pyc,,
fairseq/modules/__pycache__/fp32_instance_norm.cpython-311.pyc,,
fairseq/modules/__pycache__/gelu.cpython-311.pyc,,
fairseq/modules/__pycache__/grad_multiply.cpython-311.pyc,,
fairseq/modules/__pycache__/gumbel_vector_quantizer.cpython-311.pyc,,
fairseq/modules/__pycache__/kmeans_attention.cpython-311.pyc,,
fairseq/modules/__pycache__/kmeans_vector_quantizer.cpython-311.pyc,,
fairseq/modules/__pycache__/layer_drop.cpython-311.pyc,,
fairseq/modules/__pycache__/layer_norm.cpython-311.pyc,,
fairseq/modules/__pycache__/learned_positional_embedding.cpython-311.pyc,,
fairseq/modules/__pycache__/lightweight_convolution.cpython-311.pyc,,
fairseq/modules/__pycache__/linearized_convolution.cpython-311.pyc,,
fairseq/modules/__pycache__/location_attention.cpython-311.pyc,,
fairseq/modules/__pycache__/lstm_cell_with_zoneout.cpython-311.pyc,,
fairseq/modules/__pycache__/multihead_attention.cpython-311.pyc,,
fairseq/modules/__pycache__/positional_embedding.cpython-311.pyc,,
fairseq/modules/__pycache__/positional_encoding.cpython-311.pyc,,
fairseq/modules/__pycache__/quant_noise.cpython-311.pyc,,
fairseq/modules/__pycache__/rotary_positional_embedding.cpython-311.pyc,,
fairseq/modules/__pycache__/same_pad.cpython-311.pyc,,
fairseq/modules/__pycache__/scalar_bias.cpython-311.pyc,,
fairseq/modules/__pycache__/sinusoidal_positional_embedding.cpython-311.pyc,,
fairseq/modules/__pycache__/sparse_multihead_attention.cpython-311.pyc,,
fairseq/modules/__pycache__/sparse_transformer_sentence_encoder.cpython-311.pyc,,
fairseq/modules/__pycache__/sparse_transformer_sentence_encoder_layer.cpython-311.pyc,,
fairseq/modules/__pycache__/transformer_layer.cpython-311.pyc,,
fairseq/modules/__pycache__/transformer_sentence_encoder.cpython-311.pyc,,
fairseq/modules/__pycache__/transformer_sentence_encoder_layer.cpython-311.pyc,,
fairseq/modules/__pycache__/transpose_last.cpython-311.pyc,,
fairseq/modules/__pycache__/unfold.cpython-311.pyc,,
fairseq/modules/__pycache__/vggblock.cpython-311.pyc,,
fairseq/modules/adaptive_input.py,sha256=hUxijiA520fT8UDm3Ii6c3iezIeA-kn8jnH7EC0ajRE,2565
fairseq/modules/adaptive_softmax.py,sha256=OkLr8DVCJ4zdDTk-DLhqHozmBK1XzRTZXQ-O7ZKHPlQ,8789
fairseq/modules/base_layer.py,sha256=gpPtLKMOOaVyJ2lJqH_JBdLmMH32uavVx7PSVw8-_IE,6693
fairseq/modules/beamable_mm.py,sha256=FyrQH7I1vB0r_tusJVFj-d_9gEbK1y9q3e2s8TtSuuA,1763
fairseq/modules/character_token_embedder.py,sha256=ba0JdzYiB6nnNvZNtsYfhwgLz8ZjSDKggRR-H-KIPNc,6974
fairseq/modules/checkpoint_activations.py,sha256=X1qCGZvbE04c0W-J_VAxoKp3JBqVe5KbDycQBdSuT8A,8825
fairseq/modules/conformer_layer.py,sha256=c9B8I7CV03h5HypoyafB64yvB5z8uOYi5j1kgnb5CXk,9130
fairseq/modules/conv_tbc.py,sha256=ScYLk2rU9I5Bw311f_d4s_0Gy5OSj-mB90n_XSllkVY,1683
fairseq/modules/cross_entropy.py,sha256=qSmUNAO3BgqprkmsY7qfH23RfLu_4lqN-OQCKROobHk,1871
fairseq/modules/downsampled_multihead_attention.py,sha256=iRGLdzPP27AMSrZ96ZqYk9zeKmF4orLjXqhNRw6sSTQ,10672
fairseq/modules/dynamic_convolution.py,sha256=Ey_GY1vJUpUG9QgEW7OD76gqvYDSSph_oT-q1wbkKmM,11802
fairseq/modules/dynamic_crf_layer.py,sha256=Akhw07SIGHkl6S5mTBkFJJDLxnFxtr17qHFIOu7nFTY,7717
fairseq/modules/dynamicconv_layer/__init__.py,sha256=ZbnvKemaufTGOboM7ecz_Zt6njTjJbpO7TRbqiVeC1o,234
fairseq/modules/dynamicconv_layer/__pycache__/__init__.cpython-311.pyc,,
fairseq/modules/dynamicconv_layer/__pycache__/cuda_function_gen.cpython-311.pyc,,
fairseq/modules/dynamicconv_layer/__pycache__/dynamicconv_layer.cpython-311.pyc,,
fairseq/modules/dynamicconv_layer/__pycache__/setup.cpython-311.pyc,,
fairseq/modules/dynamicconv_layer/cuda_function_gen.py,sha256=Pq1dimvEJP6Uxx540iIBxpL8CRQaDIfYjJFAVSymYUY,6866
fairseq/modules/dynamicconv_layer/dynamicconv_layer.py,sha256=S6SG2_3-jI6ZlgjW6YB7gto2Euq7_RS_WwcMnT0tsbU,8922
fairseq/modules/dynamicconv_layer/setup.py,sha256=2_rzNi0uMmjOaWn_eSNabaUCJ8Hc932QKagpp7pSXzk,602
fairseq/modules/ema_module.py,sha256=fSZcDerAyC_LWnP1CVHSwoqgiJOdvJMZm-r_tyOw4kQ,4872
fairseq/modules/espnet_multihead_attention.py,sha256=dr4GcWwFQ2QmtZNuxqamQAES7UiJfCgnAzK38v7HZj4,9673
fairseq/modules/fairseq_dropout.py,sha256=WdAAC5qbXrUFkr8LXTCKA5eUZGi9D1kiTMQHzSi00MU,1703
fairseq/modules/fp32_batch_norm.py,sha256=n1g7WStvL0GKEmgSd51hjtsYvRWFSXZv09B8YH50AHY,1320
fairseq/modules/fp32_group_norm.py,sha256=0QvuegEHBSb3HIfaRKm9kps5bF9NULG0Dtc0JmL_Mfg,727
fairseq/modules/fp32_instance_norm.py,sha256=nkLQbqErR2g-XPhPTp680HK3B2KwCUKbt0XhWqJ8a_U,1238
fairseq/modules/gelu.py,sha256=sejvxA0sWu-qSaxyyywWnBclOQhVcABf_JjzApMz12M,706
fairseq/modules/grad_multiply.py,sha256=6bXIJ8oFuOS7tF4lQdEy_WXpgV2UrK51NTyMkefYNt0,442
fairseq/modules/gumbel_vector_quantizer.py,sha256=f3tLlpVu1laA641kP4DsXWsJZe0QzjkK2f9nc3rdBgs,6891
fairseq/modules/kmeans_attention.py,sha256=0U-q6hMSyQbwaIVZPNQJrymhrPPf_lCXyv05-etjiUk,21840
fairseq/modules/kmeans_vector_quantizer.py,sha256=6MA5sfo_M2-u9mf8tYTHeezFWD1hK_TSeP3LKq25p9k,4170
fairseq/modules/layer_drop.py,sha256=JRVWujYbCUvX0ViOyHUitUP28ew8zPLJuen2w87Q6-U,1409
fairseq/modules/layer_norm.py,sha256=JapHNh0tic3Jrl88h2a6lujk0gvNrs8AtxlY25I_1Ok,1524
fairseq/modules/learned_positional_embedding.py,sha256=jMWeRF1rrO2tMXy86Fnl5CZyA5T39LOgTQVkxidVBNo,2259
fairseq/modules/lightconv_layer/__init__.py,sha256=Y5iSamsx6fhQOLJ9QymUCtv4HH2OnW7fFecOadI1Zhk,230
fairseq/modules/lightconv_layer/__pycache__/__init__.cpython-311.pyc,,
fairseq/modules/lightconv_layer/__pycache__/cuda_function_gen.cpython-311.pyc,,
fairseq/modules/lightconv_layer/__pycache__/lightconv_layer.cpython-311.pyc,,
fairseq/modules/lightconv_layer/__pycache__/setup.cpython-311.pyc,,
fairseq/modules/lightconv_layer/cuda_function_gen.py,sha256=3O1PrnWgMXhZBGFt520_a3Uk-_RnS0NiK8epj0FT4Tw,9642
fairseq/modules/lightconv_layer/lightconv_layer.py,sha256=vWJGsnq7DN2mD8XUwXR7DZXulJs8MIWiUkXFAGh5Plw,4799
fairseq/modules/lightconv_layer/setup.py,sha256=pTCnKDy64VuLU6em8hH9ozfrNffrunmRJu92UNq-m3g,581
fairseq/modules/lightweight_convolution.py,sha256=sx5_zM0KnJU_C1-E9GglCUkhAiPZm9dbSfy99vD4giA,10919
fairseq/modules/linearized_convolution.py,sha256=FJGjpFfDa0Er8Kx32_AXgMobWcMxk0ytm53urqqOhc8,4744
fairseq/modules/location_attention.py,sha256=h1IK5D1CsbONRtH_5I7wLCwcuFg0iq3o9ngBdCtEfnk,2909
fairseq/modules/lstm_cell_with_zoneout.py,sha256=PF6YR5v4OO6Qm85MnyBy98wwWcNUDLRLb9ckEwP24sk,1220
fairseq/modules/multihead_attention.py,sha256=gBUfq4ni94lArKlmPVHzjx8mwVA3sSc0pIOdipnn2KE,35045
fairseq/modules/positional_embedding.py,sha256=hyYwIwo4-tQJs-LOu0ApV6KKcpjBHbkXKHVLT_hgaI8,1293
fairseq/modules/positional_encoding.py,sha256=pNswHHP_CHLUsIhG23Nk-EHGyWTBtoBSoKjKsME1BoM,4950
fairseq/modules/quant_noise.py,sha256=BUGV8czbjkoPqbook3ejlO9421oUEeeUs65jXVSJ-yY,4005
fairseq/modules/quantization/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
fairseq/modules/quantization/__pycache__/__init__.cpython-311.pyc,,
fairseq/modules/quantization/__pycache__/quantization_options.cpython-311.pyc,,
fairseq/modules/quantization/pq/__init__.py,sha256=u15P3hx2vkeFpmcQBXozX0tj1mn8c4vw9CcXqhRTIsc,257
fairseq/modules/quantization/pq/__pycache__/__init__.cpython-311.pyc,,
fairseq/modules/quantization/pq/__pycache__/em.cpython-311.pyc,,
fairseq/modules/quantization/pq/__pycache__/pq.cpython-311.pyc,,
fairseq/modules/quantization/pq/__pycache__/utils.cpython-311.pyc,,
fairseq/modules/quantization/pq/em.py,sha256=cXpVC4lk4wdKwpaSqH0OeE6z9-pVQT8Hv97zWurIByc,7333
fairseq/modules/quantization/pq/modules/__init__.py,sha256=40RS3aykQvQX54waC8a1IitX3W_zAL76anDXq3AedXI,290
fairseq/modules/quantization/pq/modules/__pycache__/__init__.cpython-311.pyc,,
fairseq/modules/quantization/pq/modules/__pycache__/qconv.cpython-311.pyc,,
fairseq/modules/quantization/pq/modules/__pycache__/qemb.cpython-311.pyc,,
fairseq/modules/quantization/pq/modules/__pycache__/qlinear.cpython-311.pyc,,
fairseq/modules/quantization/pq/modules/qconv.py,sha256=NYxhqBiZLubIcf0-MJg_DnULcfABEuCBOARv3i4A-vA,4245
fairseq/modules/quantization/pq/modules/qemb.py,sha256=T1KC1N8861KlVCn-SX7QmsZYy6cBiF7STAwADGr8_HQ,3719
fairseq/modules/quantization/pq/modules/qlinear.py,sha256=v8tSaRVPyytlsC02txjIn5nHILQu5-6T7Zm0Hx8FWJ0,2547
fairseq/modules/quantization/pq/pq.py,sha256=ncI5V1Bs83jBWBp6oPnNwbgSPKo-lEH-_XcA50zx8Hw,4292
fairseq/modules/quantization/pq/utils.py,sha256=pBv8RbNcqR09ALnR_xsSTclayp8RO2E_OfPFNo2yo8U,13493
fairseq/modules/quantization/quantization_options.py,sha256=eAHWYIx3zsk4ZZ3_K0EgOLEwhBOGjWn4JdqlMyHG0-4,1647
fairseq/modules/quantization/scalar/__init__.py,sha256=rgBwoOaASQh4jXiNsF1k1jhdnQI27RmrwiNxrT13SsM,221
fairseq/modules/quantization/scalar/__pycache__/__init__.cpython-311.pyc,,
fairseq/modules/quantization/scalar/__pycache__/ops.cpython-311.pyc,,
fairseq/modules/quantization/scalar/__pycache__/utils.cpython-311.pyc,,
fairseq/modules/quantization/scalar/modules/__init__.py,sha256=Vy-sNXarNeyIftsJWUChUqTn5hzM4hFQnCqRXeO0Aos,339
fairseq/modules/quantization/scalar/modules/__pycache__/__init__.cpython-311.pyc,,
fairseq/modules/quantization/scalar/modules/__pycache__/qact.cpython-311.pyc,,
fairseq/modules/quantization/scalar/modules/__pycache__/qconv.cpython-311.pyc,,
fairseq/modules/quantization/scalar/modules/__pycache__/qemb.cpython-311.pyc,,
fairseq/modules/quantization/scalar/modules/__pycache__/qlinear.cpython-311.pyc,,
fairseq/modules/quantization/scalar/modules/qact.py,sha256=RktUP8nplPJlKSBw1nZxJtxHipK3TDoQwAiTh3u1hAs,3077
fairseq/modules/quantization/scalar/modules/qconv.py,sha256=Bsf0lMH6xLkyZgkUnJeM_rsEgEZAT36vgq9QzR53LdY,4448
fairseq/modules/quantization/scalar/modules/qemb.py,sha256=keoRmFuxavGhft_RKKPCVUtv7Dp7FUHQI-jTVZkOnOc,4984
fairseq/modules/quantization/scalar/modules/qlinear.py,sha256=WDxFIKfSFQ03peSFOVQwxRlKEMksbUBuB4OfVTxX5jw,3629
fairseq/modules/quantization/scalar/ops.py,sha256=zv4TwTjWCcl1g1B_1grIqzp7q71rXMijlVncRQ3iVM4,2029
fairseq/modules/quantization/scalar/utils.py,sha256=SSQ0S0R5Id1AkzTCJydxwDzZUDAuryJdoAubphXpSj4,2657
fairseq/modules/rotary_positional_embedding.py,sha256=ivsEIBYrnT5oL_ttOVuXC2QBF-mws27sRnrIz07oRUE,1851
fairseq/modules/same_pad.py,sha256=b8B7VJz75AF6nV8Y07JT2qwJwcxHOo4AmJOPzR9igzw,552
fairseq/modules/scalar_bias.py,sha256=vW4AZ7IQ0wzZrOekZG6x8jP5JGSo5omxX8B-5g-NDEY,888
fairseq/modules/sinusoidal_positional_embedding.py,sha256=pF8ERPMJmYt9Q7rMp98SPbbCXKMrjct-McY3EILUPto,3914
fairseq/modules/sparse_multihead_attention.py,sha256=gIxwLtGeENY5P1E1zXUlCrtHCIrO3Mm61fo23UEeDbE,4931
fairseq/modules/sparse_transformer_sentence_encoder.py,sha256=yY6rj-iVsoIlZCMig1YV0oSGYqAVNH-Cx4cXC0Gb98I,3155
fairseq/modules/sparse_transformer_sentence_encoder_layer.py,sha256=p4OObGX_ajutN5AgVYKNCDva6hNwG6hTm7fcBv1tRuc,1563
fairseq/modules/transformer_layer.py,sha256=WT-01nzohDkytKatZnQD5JBKx3BlFEGGv7NSbJHbCk0,27699
fairseq/modules/transformer_sentence_encoder.py,sha256=WisQUiEjH_tlvduRifu5maG-G7n25lfybfb9ManAXus,10162
fairseq/modules/transformer_sentence_encoder_layer.py,sha256=hBllYCsLvocUDIzVsnr9zMUx8vhRd18gC6SdIlC2po8,4326
fairseq/modules/transpose_last.py,sha256=_ierzhlOqUI_WRiPwRgQTlVW-iSNWy-7eXz0PYFvBGQ,550
fairseq/modules/unfold.py,sha256=KTjOMH45XQLMn8_KWesORFOudoE5rHMojgjt6AA5woQ,596
fairseq/modules/vggblock.py,sha256=93FH8GwR23w1m6NWZ5cHnHueCwd9nS-yZRjozMaAOTo,4057
fairseq/nan_detector.py,sha256=WKxG4UClkqyy_fJXmzmuo_25TfRRo_O-sow7xNle-Oc,3742
fairseq/ngram_repeat_block.py,sha256=-9RL6HhpP7OqJ0dqnOm27qtuwN_LyXSwooQ_QTbf7Yw,4106
fairseq/optim/__init__.py,sha256=RgMi7me8zVLRYq0uv0iDr_d4vuyfCUvPAmdcWp6ARdQ,1552
fairseq/optim/__pycache__/__init__.cpython-311.pyc,,
fairseq/optim/__pycache__/adadelta.cpython-311.pyc,,
fairseq/optim/__pycache__/adafactor.cpython-311.pyc,,
fairseq/optim/__pycache__/adagrad.cpython-311.pyc,,
fairseq/optim/__pycache__/adam.cpython-311.pyc,,
fairseq/optim/__pycache__/adamax.cpython-311.pyc,,
fairseq/optim/__pycache__/amp_optimizer.cpython-311.pyc,,
fairseq/optim/__pycache__/bmuf.cpython-311.pyc,,
fairseq/optim/__pycache__/composite.cpython-311.pyc,,
fairseq/optim/__pycache__/cpu_adam.cpython-311.pyc,,
fairseq/optim/__pycache__/dynamic_loss_scaler.cpython-311.pyc,,
fairseq/optim/__pycache__/fairseq_optimizer.cpython-311.pyc,,
fairseq/optim/__pycache__/fp16_optimizer.cpython-311.pyc,,
fairseq/optim/__pycache__/fused_adam.cpython-311.pyc,,
fairseq/optim/__pycache__/fused_lamb.cpython-311.pyc,,
fairseq/optim/__pycache__/nag.cpython-311.pyc,,
fairseq/optim/__pycache__/sgd.cpython-311.pyc,,
fairseq/optim/__pycache__/shard.cpython-311.pyc,,
fairseq/optim/adadelta.py,sha256=wVr09shtZ8AAx-AtvK9bnILjomqpkM3Sh74Wj8v7cjk,1835
fairseq/optim/adafactor.py,sha256=2a_gCU91d4IILcac6Mk8HgU76wJmEB1N4OYYN9jmm1U,10900
fairseq/optim/adagrad.py,sha256=4rrRSRLGdAcdJc2R3OG0KxoXiY6FtW_hMxgoOO47GMw,1279
fairseq/optim/adam.py,sha256=QkloFPJ2RE38tTTX698rDGCYVDpwvfzuiYsoz8RnjJA,9184
fairseq/optim/adamax.py,sha256=gLqJMhrcJRgqhPwbOkAGNqwgYV_KMEK0gFalJjf1aPU,6225
fairseq/optim/amp_optimizer.py,sha256=fvZDbdgHtaU2hAujhpOAk87Vs8AhcXQyv16O6JZuIVQ,3536
fairseq/optim/bmuf.py,sha256=YbnCQwiFplMSaaLlFFYZR_JRuh7JAQWsxIvoKNyvJZE,7449
fairseq/optim/composite.py,sha256=QKpGUi2njhTrFhvXT2TBW8kEtW3qv3M40UKb-m4mM3M,6757
fairseq/optim/cpu_adam.py,sha256=5THQwYvQn4PLV_DqWIvBtNwHYYawgqxqzk2UeLwHiKo,6795
fairseq/optim/dynamic_loss_scaler.py,sha256=OmjF7qpesjXS7N3KCiVcBTI8-OSoNiSce5il03tbZg4,2635
fairseq/optim/fairseq_optimizer.py,sha256=zx6Wx1RYNDfPfsYuq39j2C_D8XQxUiH1wA239CHR05A,6176
fairseq/optim/fp16_optimizer.py,sha256=RE1AaGktzyT4Xfzj5IDoo1ZzdJMDYV7WRDE5Mgg8ihc,21467
fairseq/optim/fused_adam.py,sha256=7o3MNyvnh_OR_UpppVtSB7uTVS2tR8txhVUMh0ByD9E,15188
fairseq/optim/fused_lamb.py,sha256=q9MmdRaz-6QGIY_leNHzn55SjkykoceKt4MT9AJaMT4,1834
fairseq/optim/lr_scheduler/__init__.py,sha256=Hdj-Wz67XsVtsiwbWuoh7n8wQD-jKNMgzvnqYck4_6k,1053
fairseq/optim/lr_scheduler/__pycache__/__init__.cpython-311.pyc,,
fairseq/optim/lr_scheduler/__pycache__/cosine_lr_scheduler.cpython-311.pyc,,
fairseq/optim/lr_scheduler/__pycache__/fairseq_lr_scheduler.cpython-311.pyc,,
fairseq/optim/lr_scheduler/__pycache__/fixed_schedule.cpython-311.pyc,,
fairseq/optim/lr_scheduler/__pycache__/inverse_square_root_schedule.cpython-311.pyc,,
fairseq/optim/lr_scheduler/__pycache__/manual_lr_scheduler.cpython-311.pyc,,
fairseq/optim/lr_scheduler/__pycache__/pass_through.cpython-311.pyc,,
fairseq/optim/lr_scheduler/__pycache__/polynomial_decay_schedule.cpython-311.pyc,,
fairseq/optim/lr_scheduler/__pycache__/reduce_lr_on_plateau.cpython-311.pyc,,
fairseq/optim/lr_scheduler/__pycache__/step_lr_scheduler.cpython-311.pyc,,
fairseq/optim/lr_scheduler/__pycache__/tri_stage_lr_scheduler.cpython-311.pyc,,
fairseq/optim/lr_scheduler/__pycache__/triangular_lr_scheduler.cpython-311.pyc,,
fairseq/optim/lr_scheduler/cosine_lr_scheduler.py,sha256=SiaAoHqd3nbLgMWKdnHmMUwVzCVxZjz_UoeuWKaJcmw,5301
fairseq/optim/lr_scheduler/fairseq_lr_scheduler.py,sha256=Q2xKF5pfhuuasRBFhkmL0SLJt_7rOHwqlq5VUxtqaqI,2031
fairseq/optim/lr_scheduler/fixed_schedule.py,sha256=9sq8jRe-aJeWssk0Krq26sQaqYUFGO82a5iFx8hClxk,2643
fairseq/optim/lr_scheduler/inverse_square_root_schedule.py,sha256=_5lK2SoSIabIGC4g_b9A9uDka8FR5JPNKTu4Zu5zi8s,3228
fairseq/optim/lr_scheduler/manual_lr_scheduler.py,sha256=EOxqOA71QQ3Yj6C5bXbGYKpN01SvrhaSC3CO_lHIVP4,4174
fairseq/optim/lr_scheduler/pass_through.py,sha256=77vGEI6gUKL7dBSTySvQguG8EShGc3HtuMmDKpeJZvc,1445
fairseq/optim/lr_scheduler/polynomial_decay_schedule.py,sha256=HtlFWzvbo-iKinTiRHvw-3-aixkSp-Nj-8_sG8L3tJw,3302
fairseq/optim/lr_scheduler/reduce_lr_on_plateau.py,sha256=Zhb4G5SAIIE2CA8jfH8KtzNud7GTyWiK9FkMwV8j0Uw,5047
fairseq/optim/lr_scheduler/step_lr_scheduler.py,sha256=A3GjEC4U4BT7IQZouIHQviNVr1XD7w8PkrfACGO22R0,3154
fairseq/optim/lr_scheduler/tri_stage_lr_scheduler.py,sha256=bttIOMqUMn2YuNegkl-P_60rdEQFvgv04g7bZCR82Co,5766
fairseq/optim/lr_scheduler/triangular_lr_scheduler.py,sha256=i_i2465eZ8rA8tMdVF5iTYnYp3acLweqQ90nI1GQRi8,2760
fairseq/optim/nag.py,sha256=M43NlHjdIA6jcigMEAEYesx-07d6QzWNiztN2yG5lnQ,3731
fairseq/optim/sgd.py,sha256=zIotyCM29CtwrB4qNyFpqr_xnoArAqvilWsKZ3rMZ24,1442
fairseq/optim/shard.py,sha256=w3DR3s-VIu0rQrgwwq2kZRmtLRP1rB7msJPoFxpFEWs,1624
fairseq/options.py,sha256=nat0eF2eyZa2cLyu2otkd2a12443V6F7drt1epFZ3zk,15440
fairseq/pdb.py,sha256=F3UF20mnc78AgLM_YNpoq1I5ekB7pXWsrJcVAfyFfBU,1089
fairseq/quantization_utils.py,sha256=xbLZsgSvLmR3jKKEwruf5gYrn3crlnMuWu2hZT84VSQ,5507
fairseq/registry.py,sha256=PQWKXBr-oVvKl1vwK2xfa578Gkyw3O6cYreQKkA2NqE,3449
fairseq/scoring/__init__.py,sha256=QOj4lOEPBwZOcLu7uyulQv9DCBOm0VfIj8rlHZMUgOo,1399
fairseq/scoring/__pycache__/__init__.cpython-311.pyc,,
fairseq/scoring/__pycache__/bertscore.cpython-311.pyc,,
fairseq/scoring/__pycache__/bleu.cpython-311.pyc,,
fairseq/scoring/__pycache__/chrf.cpython-311.pyc,,
fairseq/scoring/__pycache__/meteor.cpython-311.pyc,,
fairseq/scoring/__pycache__/tokenizer.cpython-311.pyc,,
fairseq/scoring/__pycache__/wer.cpython-311.pyc,,
fairseq/scoring/bertscore.py,sha256=6giFuiN0lOHzpYcgAvrPaaJiKzJXPGqC5oXqZ5ZlTWU,1349
fairseq/scoring/bleu.py,sha256=7wahJDimVx6DPptkFyppzXQrz-unBD5tVAmRezR9eGg,5347
fairseq/scoring/chrf.py,sha256=2mpgbJh4eWXmQSVl0OSD_q3yKW4uKomTgx_VRJbRXg4,954
fairseq/scoring/meteor.py,sha256=Ds624du0iEIFh8V2E9ESDgNHdAFTf2gqR3OF-WHAb7k,1164
fairseq/scoring/tokenizer.py,sha256=UKtwnBKK9zQTNiUdIvVdwPknnxkQnz_TVS36D1q5A9E,2599
fairseq/scoring/wer.py,sha256=crZQq8kpLRoQJRxBRPZM0bTNuAj5-PqstEtdK8X5gxo,2019
fairseq/search.py,sha256=iLGscNUYYKVjb-Yi1QBe_N9T1DGr7kf7wsJK8NG847c,31369
fairseq/sequence_generator.py,sha256=8faCevHgpU8o_ecLBFywvMYPRtSW_KBXUW__p4nInPI,40455
fairseq/sequence_scorer.py,sha256=v_-mcfM2H1DD8gHNyuQJtYGbtkEd1BRhderM8NEkIbs,5450
fairseq/speech_generator.py,sha256=UPOlr5A16m1GTsq9HqjjpAH9XLPJlQ1-bCYKghx_G-M,8840
fairseq/tasks/__init__.py,sha256=kfHBaebSRo_ONPAyoSs0H6TzAOzFkDClDANAMXc5k0g,4365
fairseq/tasks/__pycache__/__init__.cpython-311.pyc,,
fairseq/tasks/__pycache__/audio_finetuning.cpython-311.pyc,,
fairseq/tasks/__pycache__/audio_pretraining.cpython-311.pyc,,
fairseq/tasks/__pycache__/cross_lingual_lm.cpython-311.pyc,,
fairseq/tasks/__pycache__/denoising.cpython-311.pyc,,
fairseq/tasks/__pycache__/fairseq_task.cpython-311.pyc,,
fairseq/tasks/__pycache__/frm_text_to_speech.cpython-311.pyc,,
fairseq/tasks/__pycache__/hubert_pretraining.cpython-311.pyc,,
fairseq/tasks/__pycache__/language_modeling.cpython-311.pyc,,
fairseq/tasks/__pycache__/legacy_masked_lm.cpython-311.pyc,,
fairseq/tasks/__pycache__/masked_lm.cpython-311.pyc,,
fairseq/tasks/__pycache__/multilingual_denoising.cpython-311.pyc,,
fairseq/tasks/__pycache__/multilingual_language_modeling.cpython-311.pyc,,
fairseq/tasks/__pycache__/multilingual_masked_lm.cpython-311.pyc,,
fairseq/tasks/__pycache__/multilingual_translation.cpython-311.pyc,,
fairseq/tasks/__pycache__/online_backtranslation.cpython-311.pyc,,
fairseq/tasks/__pycache__/semisupervised_translation.cpython-311.pyc,,
fairseq/tasks/__pycache__/sentence_prediction.cpython-311.pyc,,
fairseq/tasks/__pycache__/sentence_prediction_adapters.cpython-311.pyc,,
fairseq/tasks/__pycache__/sentence_ranking.cpython-311.pyc,,
fairseq/tasks/__pycache__/simultaneous_translation.cpython-311.pyc,,
fairseq/tasks/__pycache__/speech_to_speech.cpython-311.pyc,,
fairseq/tasks/__pycache__/speech_to_text.cpython-311.pyc,,
fairseq/tasks/__pycache__/speech_ulm_task.cpython-311.pyc,,
fairseq/tasks/__pycache__/text_to_speech.cpython-311.pyc,,
fairseq/tasks/__pycache__/translation.cpython-311.pyc,,
fairseq/tasks/__pycache__/translation_from_pretrained_bart.cpython-311.pyc,,
fairseq/tasks/__pycache__/translation_from_pretrained_xlm.cpython-311.pyc,,
fairseq/tasks/__pycache__/translation_lev.cpython-311.pyc,,
fairseq/tasks/__pycache__/translation_multi_simple_epoch.cpython-311.pyc,,
fairseq/tasks/audio_finetuning.py,sha256=YXNhSFlwwNX6p2n_fADyWbAKFsH4lWTl6c2M5a8Aujw,13503
fairseq/tasks/audio_pretraining.py,sha256=uIZQgN3UBDEZScVAa_UT0q5crYWprMqox_WftUw68J8,7781
fairseq/tasks/cross_lingual_lm.py,sha256=_vPUf_43dIvwngu_g1H-J6t6ZDtkuHNb76xVi7zJRhs,6454
fairseq/tasks/denoising.py,sha256=nGzxk3flcmLk8rr8YxUKhHIWDpW6mUaVxfZ6KtCtauY,8960
fairseq/tasks/fairseq_task.py,sha256=cxXlRjWHyyA6x-Prdr0j9CvQjL9FzeaCqBHEaa8CDpw,26528
fairseq/tasks/frm_text_to_speech.py,sha256=BGPe-kNZCHGX2dffwjp4ZwkpxWf2ZsForLnY0vkBow8,2093
fairseq/tasks/hubert_pretraining.py,sha256=_3T7rOnvjP_QTIgHddwftlS9Mbd1O_OFlih9yg17Km4,6225
fairseq/tasks/language_modeling.py,sha256=Cr0kd3yCJPcrkUnn0NqB9qpGKbbGoNNmrJufjqIp-Go,13952
fairseq/tasks/legacy_masked_lm.py,sha256=RuJIZgSfekvhNqGw_BS4pwrcACAc7myNIgJv6JCTxR8,5010
fairseq/tasks/masked_lm.py,sha256=sttQRVi3w849IDFMTcJG-YrsaxPpOI1SI2hcEAuxJLw,9017
fairseq/tasks/multilingual_denoising.py,sha256=8ofRkxxBID2jRph8LDFNYa0UwYrERbgJhB0kW0j2Ne0,8756
fairseq/tasks/multilingual_language_modeling.py,sha256=oGwsmsY4ZnZrS3cLzVj5JhfamvE8BTN4a_1gEtCGlaQ,22960
fairseq/tasks/multilingual_masked_lm.py,sha256=AiAD12nv7pMSQm31Cf-Rw9I08dcn0qZD1bybSsPi8qY,12144
fairseq/tasks/multilingual_translation.py,sha256=fBEJdScGCW4zwg6NqsmVAUoCC2pB0aZV7erXsg_CjMs,18165
fairseq/tasks/online_backtranslation.py,sha256=KKbfe9VVE6qQTQ8fPfLC7B2v5PiTYpNasN3DWq4UgpI,28618
fairseq/tasks/semisupervised_translation.py,sha256=y5m4craf5HefcFqDUhPzikas_1XsjvYCO1_PRhflwyM,20415
fairseq/tasks/sentence_prediction.py,sha256=E9Gz-cmWRbFZNL6bjw_onNXemp3Zd1lCDAunLGRYPmE,9201
fairseq/tasks/sentence_prediction_adapters.py,sha256=-FyZKAS5-w16h-YoMVchYiRKhcgF8rJ3N4msBTtTuRc,1994
fairseq/tasks/sentence_ranking.py,sha256=dMUJDKUTHaRL8MYGXunMFxLTACmkfsVkwHsag1XBuyI,7037
fairseq/tasks/simultaneous_translation.py,sha256=fr6547GB7d1XsqDzPT8YMP79o1P-Q-5C3yx13Ak1MiQ,1226
fairseq/tasks/speech_to_speech.py,sha256=OKY2hrNY7tQTntub-jAd0knvd160DwguXkvBsAqwqYI,19402
fairseq/tasks/speech_to_text.py,sha256=qEa8tRqCAjlJXNtHtUU2AxYkMVoFOUXPWnABUOtdNAo,6695
fairseq/tasks/speech_ulm_task.py,sha256=cL6JclRUUijwc8wCL--YocUarrN6tCVIls64aTJC93U,7533
fairseq/tasks/text_to_speech.py,sha256=Xds7EWT1N-2W71TZKzt6HIcpaOhgiflF9Luu49v0z1Q,17256
fairseq/tasks/translation.py,sha256=vhtXXY8zNx4eerPatuw0XLRtt0knP5d_5Lt1F2JJtD0,17938
fairseq/tasks/translation_from_pretrained_bart.py,sha256=Fhm0MkDs3FlW5Q7xvKWhkjkKpmSUNhHBTCQdZbpCWfI,5243
fairseq/tasks/translation_from_pretrained_xlm.py,sha256=TpYTaEo-pTd9sARG7YaIh9Qfj8R2khBQOFknytWqmiE,1294
fairseq/tasks/translation_lev.py,sha256=UM7SEO4vrHcQMYYSNc_CG9DFWl7sDk-5P9kI0U0Xp5I,7416
fairseq/tasks/translation_multi_simple_epoch.py,sha256=23rylonzx9pMH0pq5yunsw0GARlm2IbYLi78i6tZNpI,17926
fairseq/token_generation_constraints.py,sha256=QL6YqUs85my2sf6PCPbnGC38VP8w2KInKZHdsxQeogQ,16556
fairseq/tokenizer.py,sha256=VivgMeL06Qrj1opy5x1p7KpX_zuCszpjP6xej8ltluI,346
fairseq/trainer.py,sha256=UEJNObgCLRWzRUpbDHwOYjjKXOYB7ZrP_wKfZ0YDhBA,65747
fairseq/utils.py,sha256=Q_OtQi_hCZbmbAu4DYJpucqwyBDxAlYUP9jUl5__Zjs,26873
fairseq/version.py,sha256=frhauC2oQiu6OiJW1p4GNzUEkyBHBPCUsbnMAYAy-8g,24
fairseq_cli/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
fairseq_cli/__pycache__/__init__.cpython-311.pyc,,
fairseq_cli/__pycache__/eval_lm.cpython-311.pyc,,
fairseq_cli/__pycache__/generate.cpython-311.pyc,,
fairseq_cli/__pycache__/hydra_train.cpython-311.pyc,,
fairseq_cli/__pycache__/interactive.cpython-311.pyc,,
fairseq_cli/__pycache__/preprocess.cpython-311.pyc,,
fairseq_cli/__pycache__/score.cpython-311.pyc,,
fairseq_cli/__pycache__/train.cpython-311.pyc,,
fairseq_cli/__pycache__/validate.cpython-311.pyc,,
fairseq_cli/eval_lm.py,sha256=57vlJ1xAkF7FuaRC92v5kXle3evC_bIFlh1ekGzlU3c,11960
fairseq_cli/generate.py,sha256=lG22TrEwVVekni86wRqyj9n_hXv7FpCWddziOO-Bwuc,15805
fairseq_cli/hydra_train.py,sha256=yVMBJh5lzmlxAuhEiLIUIcdatGUKGCFbNQeUlHCYEhM,2714
fairseq_cli/interactive.py,sha256=PcLvqmVMJW_VwBEMmRhpsTHk7ETt65v_TrbdTb8bAGA,11465
fairseq_cli/preprocess.py,sha256=mJqo-w0tQmKU4Jy0OzPvtIYH1JdRy7ToKWrK015bDEo,12218
fairseq_cli/score.py,sha256=bbc5WIc-HW0dWktgzW4EQS4swVFSHmhQRrFvISu-p0E,3287
fairseq_cli/train.py,sha256=qG1kcKJlAlgH-3fz9xLEXMFRsz2laINnpFFjyLg1uno,19932
fairseq_cli/validate.py,sha256=EH4yOsYiGA_ARgcEnWyY5JleZ9yFBHu4u5MkqM5_H8g,5228
