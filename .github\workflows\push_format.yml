name: Standardize Code Format

on:
  push:
    branches:
      - main
      - dev

jobs:
  push_format:
    runs-on: ubuntu-latest

    permissions:
      contents: write
      pull-requests: write

    steps:
      - uses: actions/checkout@v3
        with:
          ref: ${{github.ref_name}}

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install Black
        run: pip install "black[jupyter]"

      - name: Run Black
        # run: black $(git ls-files '*.py')
        run: black .

      - name: Commit Back
        continue-on-error: true
        id: commitback
        run: |
          git config --local user.email "github-actions[bot]@users.noreply.github.com"
          git config --local user.name "github-actions[bot]"
          git add --all
          git commit -m "chore(format): run black on ${{github.ref_name}}"

      - name: Create Pull Request
        if: steps.commitback.outcome == 'success'
        continue-on-error: true
        uses: peter-evans/create-pull-request@v5
        with:
          delete-branch: true
          body: "Automatically apply code formatter change"
          title: "chore(format): run black on ${{github.ref_name}}"
          commit-message: "chore(format): run black on ${{github.ref_name}}"
          branch: formatter-${{github.ref_name}}
