[tool.poetry]
name = "rvc-beta"
version = "0.1.0"
description = ""
authors = ["lj1995"]
license = "MIT"

[tool.poetry.dependencies]
python = "^3.9"
torch = "2.4.0"
torchaudio = "2.4.0"
Cython = "^3.0.11"
gradio = "3.34.0"
pydub = ">=0.25.1"
soundfile = ">=0.12.1"
ffmpeg-python = ">=0.2.0"
tensorboardX = "^*******"
fairseq = "0.12.2"
faiss-cpu = "1.7.3"
Jinja2 = ">=3.1.2"
json5 = "^0.9.25"
librosa = "0.9.1"
llvmlite = "0.39.0"
Markdown = "^3.6"
matplotlib = ">=3.7.0"
matplotlib-inline = ">=0.1.3"
numba = "0.56.4"
numpy = "1.23.5"
scipy = "1.13.1"
praat-parselmouth = ">=0.4.2"
Pillow = ">=9.1.1"
pyworld = "0.3.2"
resampy = ">=0.4.2"
scikit-learn = "^1.5.1"
tensorboard = "^2.17.0"
tqdm = ">=4.63.1"
tornado = ">=6.1"
Werkzeug = ">=2.2.3"
uc-micro-py = ">=1.0.1"
sympy = ">=1.11.1"
tabulate = ">=0.8.10"
PyYAML = ">=6.0"
pyasn1 = ">=0.4.8"
pyasn1-modules = ">=0.2.8"
fsspec = ">=2022.11.0"
absl-py = ">=1.2.0"
audioread = "^3.0.1"
uvicorn = ">=0.21.1"
colorama = ">=0.4.5"
torchcrepe = "0.0.20"
python-dotenv = ">=1.0.0"
av = "^12.3.0"
joblib = ">=1.1.0"
httpx = "^0.27.0"
onnxruntime-gpu = "^1.18.1"
fastapi = "0.88"
torchfcpe = "^0.0.4"
ffmpy = "0.3.1"
torchvision = "0.19.0"
[tool.poetry.dev-dependencies]

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"
