Speech Synthesis (S^2)
===
[https://arxiv.org/abs/2109.06912](https://arxiv.org/abs/2109.06912)

Speech synthesis with fairseq.

## Features

- Autoregressive and non-autoregressive models
- Multi-speaker synthesis
- Audio preprocessing (denoising, VAD, etc.) for less curated data
- Automatic metrics for model development
- Similar data configuration as [S2T](../speech_to_text/README.md)


## Examples
- [Single-speaker synthesis on LJSpeech](docs/ljspeech_example.md)
- [Multi-speaker synthesis on VCTK](docs/vctk_example.md)
- [Multi-speaker synthesis on Common Voice](docs/common_voice_example.md)


## Citation
Please cite as:
```
@article{wang2021fairseqs2,
  title={fairseq S\^{} 2: A Scalable and Integrable Speech Synthesis Toolkit},
  author={<PERSON>, <PERSON> and <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON>, <PERSON>},
  journal={arXiv preprint arXiv:2109.06912},
  year={2021}
}

@inproceedings{ott2019fairseq,
  title = {fairseq: A Fast, Extensible Toolkit for Sequence Modeling},
  author = {Myle Ott and <PERSON>v and <PERSON>evski and <PERSON> and Sam <PERSON> and <PERSON> and <PERSON> Grangier and <PERSON> Auli},
  booktitle = {Proceedings of NAACL-HLT 2019: Demonstrations},
  year = {2019},
}
```
