name: Merge dev into main

on:
  workflow_dispatch:

jobs:
  sync_dev:
    runs-on: ubuntu-latest

    permissions:
      contents: write
      pull-requests: write

    steps:
      - uses: actions/checkout@v3
        with:
          ref: main

      - name: Create Pull Request
        run: |
          gh pr create --title "chore(sync): merge dev into main" --body "Merge dev to main" --base main --head dev
        env:
          GH_TOKEN: ${{ github.token }}
