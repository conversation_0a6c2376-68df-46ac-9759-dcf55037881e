# Speech to speech translation (S2ST)

We provide the implementation and resources for the following work on speech-to-speech translation (S2ST):

* [Direct speech-to-speech translation with discrete units (<PERSON> et al. 2021)](docs/direct_s2st_discrete_units.md)
* [Textless Speech-to-Speech Translation on Real Data (<PERSON> et al. 2021)](docs/textless_s2st_real_data.md)
* [Enhanced Direct Speech-to-Speech Translation Using Self-supervised Pre-training and Data Augmentation](docs/enhanced_direct_s2st_discrete_units.md)
