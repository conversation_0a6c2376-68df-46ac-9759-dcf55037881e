name: Check Pull Format

on:
  pull_request_target:
    types: [opened, reopened]

jobs:
  # This workflow closes invalid PR
  close_pr:
    # The type of runner that the job will run on
    runs-on: ubuntu-latest
    permissions: write-all

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      - name: Close PR if it is not pointed to dev branch
        if: github.event.pull_request.base.ref != 'dev'
        uses: superbrothers/close-pull-request@v3
        with:
          # Optional. Post a issue comment just before closing a pull request.
          comment: "Invalid PR to `non-dev` branch `${{ github.event.pull_request.base.ref }}`."

  pull_format:
    runs-on: ubuntu-latest
    permissions:
      contents: write

    continue-on-error: true

    steps:
      - name: Checkout
        continue-on-error: true
        uses: actions/checkout@v3
        with:
          ref: ${{ github.head_ref }}
          fetch-depth: 0

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install Black
        run: pip install "black[jupyter]"

      - name: Run Black
        # run: black $(git ls-files '*.py')
        run: black .
