version: "3.8"
services:
  rvc:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: rvc
    volumes:
      - ./weights:/app/assets/weights
      - ./opt:/app/opt
      # - ./dataset:/app/dataset # you can use this folder in order to provide your dataset for model training
    ports:
      - 7865:7865
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]