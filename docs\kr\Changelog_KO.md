### 2023년 10월 6일 업데이트

실시간 음성 변환을 위한 인터페이스인 go-realtime-gui.bat/gui_v1.py를 제작했습니다(사실 이는 이미 존재했었습니다). 이번 업데이트는 주로 실시간 음성 변환 성능을 최적화하는 데 중점을 두었습니다. 0813 버전과 비교하여:

- 1.  인터페이스 조작 최적화: 매개변수 핫 업데이트(매개변수 조정 시 중단 후 재시작 필요 없음), 모델 지연 로딩(이미 로드된 모델은 재로드 필요 없음), 음량 인자 매개변수 추가(음량을 입력 오디오에 가깝게 조정)
- 2.  내장된 노이즈 감소 효과 및 속도 최적화
- 3.  추론 속도 크게 향상

입력 및 출력 장치는 동일한 유형을 선택해야 합니다. 예를 들어, 모두 MME 유형을 선택해야 합니다.

1006 버전의 전체 업데이트는 다음과 같습니다:

- 1.  rmvpe 음성 피치 추출 알고리즘의 효과를 계속해서 향상, 특히 남성 저음역에 대한 개선이 큼
- 2.  추론 인터페이스 레이아웃 최적화

### 2023년 08월 13일 업데이트

1-정기적인 버그 수정

- 최소 총 에포크 수를 1로 변경하고, 최소 총 에포크 수를 2로 변경합니다.
- 사전 훈련(pre-train) 모델을 사용하지 않는 훈련 오류 수정
- 반주 보컬 분리 후 그래픽 메모리 지우기
- 페이즈 저장 경로 절대 경로를 상대 경로로 변경
- 공백이 포함된 경로 지원(훈련 세트 경로와 실험 이름 모두 지원되며 더 이상 오류가 보고되지 않음)
- 파일 목록에서 필수 utf8 인코딩 취소
- 실시간 음성 변경 중 faiss 검색으로 인한 CPU 소모 문제 해결

2-키 업데이트

- 현재 가장 강력한 오픈 소스 보컬 피치 추출 모델 RMVPE를 훈련하고, 이를 RVC 훈련, 오프라인/실시간 추론에 사용하며, PyTorch/Onx/DirectML을 지원합니다.
- 파이토치\_DML을 통한 AMD 및 인텔 그래픽 카드 지원
  (1) 실시간 음성 변화 (2) 추론 (3) 보컬 반주 분리 (4) 현재 지원되지 않는 훈련은 CPU 훈련으로 전환, Onnx_Dml을 통한 gpu의 RMVPE 추론 지원

### 2023년 6월 18일 업데이트

- v2 버전에서 새로운 32k와 48k 사전 학습 모델을 추가.
- non-f0 모델들의 추론 오류 수정.
- 학습 세트가 1시간을 넘어가는 경우, 인덱스 생성 단계에서 minibatch-kmeans을 사용해, 학습속도 가속화.
- [huggingface](https://huggingface.co/spaces/lj1995/vocal2guitar)에서 vocal2guitar 제공.
- 데이터 처리 단계에서 이상 값 자동으로 제거.
- ONNX로 내보내는(export) 옵션 탭 추가.

업데이트에 적용되지 않았지만 시도한 것들 :

- ~~시계열 차원을 추가하여 특징 검색을 진행했지만, 유의미한 효과는 없었습니다.~~
- ~~PCA 차원 축소를 추가하여 특징 검색을 진행했지만, 유의미한 효과는 없었습니다.~~
- ~~ONNX 추론을 지원하는 것에 실패했습니다. nsf 생성시, Pytorch가 필요하기 때문입니다.~~
- ~~훈련 중에 입력에 대한 음고, 성별, 이퀄라이저, 노이즈 등 무작위로 강화하는 것에, 유의미한 효과는 없었습니다.~~

추후 업데이트 목록:

- ~~Vocos-RVC (소형 보코더) 통합 예정.~~
- ~~학습 단계에 음고 인식을 위한 Crepe 지원 예정.~~
- ~~Crepe의 정밀도를 REC-config와 동기화하여 지원 예정.~~
- FO 에디터 지원 예정.

### 2023년 5월 28일 업데이트

- v2 jupyter notebook 추가, 한국어 업데이트 로그 추가, 의존성 모듈 일부 수정.
- 무성음 및 숨소리 보호 모드 추가.
- crepe-full pitch 감지 지원.
- UVR5 보컬 분리: 디버브 및 디-에코 모델 지원.
- index 이름에 experiment 이름과 버전 추가.
- 배치 음성 변환 처리 및 UVR5 보컬 분리 시, 사용자가 수동으로 출력 오디오의 내보내기(export) 형식을 선택할 수 있도록 지원.
- 32k 훈련 모델 지원 종료.

### 2023년 5월 13일 업데이트

- 원클릭 패키지의 이전 버전 런타임 내, 불필요한 코드(lib.infer_pack 및 uvr5_pack) 제거.
- 훈련 세트 전처리의 유사 다중 처리 버그 수정.
- Harvest 피치 인식 알고리즘에 대한 중위수 필터링 반경 조정 추가.
- 오디오 내보낼 때, 후처리 리샘플링 지원.
- 훈련에 대한 다중 처리 "n_cpu" 설정이 "f0 추출"에서 "데이터 전처리 및 f0 추출"로 변경.
- logs 폴더 하의 인덱스 경로를 자동으로 감지 및 드롭다운 목록 기능 제공.
- 탭 페이지에 "자주 묻는 질문과 답변" 추가. (github RVC wiki 참조 가능)
- 동일한 입력 오디오 경로를 사용할 때 추론, Harvest 피치를 캐시.
  (주의: Harvest 피치 추출을 사용하면 전체 파이프라인은 길고 반복적인 피치 추출 과정을 거치게됩니다. 캐싱을 하지 않는다면, 첫 inference 이후의 단계에서 timbre, 인덱스, 피치 중위수 필터링 반경 설정 등 대기시간이 엄청나게 길어집니다!)

### 2023년 5월 14일 업데이트

- 입력의 볼륨 캡슐을 사용하여 출력의 볼륨 캡슐을 혼합하거나 대체. (입력이 무음이거나 출력의 노이즈 문제를 최소화 할 수 있습니다. 입력 오디오의 배경 노이즈(소음)가 큰 경우 해당 기능을 사용하지 않는 것이 좋습니다. 기본적으로 비활성화 되어있는 옵션입니다. (1: 비활성화 상태))
- 추출된 소형 모델을 지정된 빈도로 저장하는 기능을 지원. (다양한 에폭 하에서의 성능을 보려고 하지만 모든 대형 체크포인트를 저장하고 매번 ckpt 처리를 통해 소형 모델을 수동으로 추출하고 싶지 않은 경우 이 기능은 매우 유용합니다)
- 환경 변수를 설정하여 서버의 전역 프록시로 인한 "연결 오류" 문제 해결.
- 사전 훈련된 v2 모델 지원. (현재 40k 버전만 테스트를 위해 공개적으로 사용 가능하며, 다른 두 개의 샘플링 비율은 아직 완전히 훈련되지 않아 보류되었습니다.)
- 추론 전, 1을 초과하는 과도한 볼륨 제한.
- 데이터 전처리 매개변수 미세 조정.

### 2023년 4월 9일 업데이트

- GPU 이용률 향상을 위해 훈련 파라미터 수정: A100은 25%에서 약 90%로 증가, V100: 50%에서 약 90%로 증가, 2060S: 60%에서 약 85%로 증가, P40: 25%에서 약 95%로 증가.
  훈련 속도가 크게 향상.
- 매개변수 기준 변경: total batch_size는 GPU당 batch_size를 의미.
- total_epoch 변경: 최대 한도가 100에서 1000으로 증가. 기본값이 10에서 20으로 증가.
- ckpt 추출이 피치를 잘못 인식하여 비정상적인 추론을 유발하는 문제 수정.
- 분산 훈련 과정에서 각 랭크마다 ckpt를 저장하는 문제 수정.
- 특성 추출 과정에 나노 특성 필터링 적용.
- 무음 입력/출력이 랜덤하게 소음을 생성하는 문제 수정. (이전 모델은 새 데이터셋으로 다시 훈련해야 합니다)

### 2023년 4월 16일 업데이트

- 로컬 실시간 음성 변경 미니-GUI 추가, go-realtime-gui.bat를 더블 클릭하여 시작.
- 훈련 및 추론 중 50Hz 이하의 주파수 대역에 대해 필터링 적용.
- 훈련 및 추론의 pyworld 최소 피치 추출을 기본 80에서 50으로 낮춤. 이로 인해, 50-80Hz 사이의 남성 저음이 무음화되지 않습니다.
- 시스템 지역에 따른 WebUI 언어 변경 지원. (현재 en_US, ja_JP, zh_CN, zh_HK, zh_SG, zh_TW를 지원하며, 지원되지 않는 경우 기본값은 en_US)
- 일부 GPU의 인식 수정. (예: V100-16G 인식 실패, P4 인식 실패)

### 2023년 4월 28일 업데이트

- Faiss 인덱스 설정 업그레이드로 속도가 더 빨라지고 품질이 향상.
- total_npy에 대한 의존성 제거. 추후의 모델 공유는 total_npy 입력을 필요로 하지 않습니다.
- 16 시리즈 GPU에 대한 제한 해제, 4GB VRAM GPU에 대한 4GB 추론 설정 제공.
- 일부 오디오 형식에 대한 UVR5 보컬 동반 분리에서의 버그 수정.
- 실시간 음성 변경 미니-GUI는 이제 non-40k 및 non-lazy 피치 모델을 지원합니다.

### 추후 계획

Features:

- 다중 사용자 훈련 탭 지원.(최대 4명)

Base model:

- 훈련 데이터셋에 숨소리 wav 파일을 추가하여, 보컬의 호흡이 노이즈로 변환되는 문제 수정.
- 보컬 훈련 세트의 기본 모델을 추가하기 위한 작업을 진행중이며, 이는 향후에 발표될 예정.
