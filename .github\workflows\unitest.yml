name: Unit Test
on: [ push, pull_request ]
jobs:
  build:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        python-version: ["3.8", "3.9", "3.10"]
        os: [ubuntu-latest]
      fail-fast: true

    steps:
    - uses: actions/checkout@master
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    - name: Install dependencies
      run: |
        sudo apt update
        sudo apt -y install ffmpeg
        sudo apt -y install -qq aria2
        aria2c --console-log-level=error -c -x 16 -s 16 -k 1M https://huggingface.co/lj1995/VoiceConversionWebUI/resolve/main/hubert_base.pt -d ./ -o hubert_base.pt
        python -m pip install --upgrade pip
        python -m pip install --upgrade setuptools
        python -m pip install --upgrade wheel
        pip install torch torchvision torchaudio
        pip install -r requirements.txt
    - name: Test step 1 & 2
      run: |
        mkdir -p logs/mi-test
        touch logs/mi-test/preprocess.log
        python infer/modules/train/preprocess.py logs/mute/0_gt_wavs 48000 8 logs/mi-test True 3.7
        touch logs/mi-test/extract_f0_feature.log
        python infer/modules/train/extract/extract_f0_print.py logs/mi-test $(nproc) pm
        python infer/modules/train/extract_feature_print.py cpu 1 0 0 logs/mi-test v1 True
