# @package _group_

defaults:
  - model: null

hydra:
  run:
    dir: ${common_eval.results_path}/beam${decoding.beam}_th${decoding.beamthreshold}_lmw${decoding.lmweight}_wrd${decoding.wordscore}_sil${decoding.silweight}
  sweep:
    dir: ${common_eval.results_path}
    subdir: beam${decoding.beam}_th${decoding.beamthreshold}_lmw${decoding.lmweight}_wrd${decoding.wordscore}_sil${decoding.silweight}

task:
  _name: hubert_pretraining
  single_target: true
  fine_tuning: true
  data: ???
  normalize: ???

decoding:
  type: fairseqlm
  lexicon: ???
  lmpath: ???
  beamthreshold: 25
  beam: 500
  lmweight: 2
  wordscore: -1
  silweight: 0
  unique_wer_file: true
common_eval:
  results_path: ???
  path: ???
  post_process: letter
dataset:
  max_tokens: 1100000
  gen_subset: ???
